extend type Mutation {
    """
    Draft a new application
    """
    draftStandardApplication(
        # module application ID
        moduleId: ObjectID!
        # customer/applicant draft
        customer: ApplicationCustomerDraft!
        # Application configuration
        configuration: StandardApplicationConfiguration!
        # trade in vehicle
        tradeInVehicle: [TradeInVehiclePayload!]!
        # financing settings
        financing: ApplicationFinanceSettings!
        # insurancing settings
        insurancing: ApplicationInsuranceSettings
        # vehicle draft
        vehicle: ApplicationVehicleDraft!
        # endpoint ID
        endpointId: ObjectID
        # dealer ID
        dealerId: ObjectID!
        # promo code ID
        promoCodeId: ObjectID
        # language ID
        languageId: ObjectID
    ): ApplicationJourney!

    """
    Draft a new application using an existing lead
    """
    draftStandardApplicationFromLead(
        # module application ID
        moduleId: ObjectID!
        # Application configuration
        configuration: StandardApplicationConfiguration!
        # trade in vehicle
        tradeInVehicle: [TradeInVehiclePayload!]!
        # financing settings
        financing: ApplicationFinanceSettings!
        # insurancing settings
        insurancing: ApplicationInsuranceSettings
        # vehicle draft
        vehicle: ApplicationVehicleDraft!
        # endpoint ID
        endpointId: ObjectID
        # dealer ID
        dealerId: ObjectID!
        # promo code ID
        promoCodeId: ObjectID
        # language ID
        languageId: ObjectID
        # lead ID
        leadId: ObjectID!
    ): ApplicationJourney!

    """
    update existing application draft
    """
    updateStandardApplicationDraft(
        token: String!
        # customer/applicant draft
        customer: ApplicationCustomerDraft!
        # Application configuration
        configuration: StandardApplicationConfiguration!
        # trade in vehicle
        tradeInVehicle: [TradeInVehiclePayload!]!
        # financing settings
        financing: ApplicationFinanceSettings!
        # insurancing settings
        insurancing: ApplicationInsuranceSettings
        # promo code ID
        promoCodeId: ObjectID
        # language ID
        languageId: ObjectID
    ): ApplicationJourney!

    """
    Update Standard Application Configuration
    """
    updateStandardApplicationConfiguration(token: String!, configuration: StandardApplicationConfiguration!): Boolean!

    """
    Update Standard Application Journey
    """
    updateStandardApplicationJourney(
        # standard application journey token
        token: String!
        # standard application configuration
        configuration: StandardApplicationConfiguration!
        # trade in vehicle
        tradeInVehicle: [TradeInVehiclePayload!]!
        # Customer Kind
        customerKind: CustomerKind!
        saveDraft: Boolean
        financing: UpdateApplicationJourneyFinancingSettings
    ): ApplicationJourney!

    """
    Applicant agrees on CnD
    """
    submitApplicantAgreements(
        # journey token
        token: String!

        # Customer Kind
        customerKind: CustomerKind!

        # agreed consent IDs
        agreedConsents: [ApplicantAgreementSettings!]!

        hasGuarantor: Boolean
    ): ApplicationJourney!

    """
    Applicant provides KYC
    """
    submitApplicantKYC(
        token: String!
        # Customer Kind
        customerKind: CustomerKind!
        fields: [LocalCustomerFieldSettings!]!
        saveDraft: Boolean
        capValues: CapValuesInput
        customerCiamId: String
        isCustomerSearchPerformed: Boolean
    ): ApplicationJourney!

    """
    Applicant provides Test Drive KYC
    """
    submitTestDriveKYC(
        token: String!
        # Customer Kind
        fields: [LocalCustomerFieldSettings!]!
    ): ApplicationJourney!

    """
    Applicant agrees on Test DriveCnD
    """
    submitTestDriveAgreements(
        # journey token
        token: String!

        # agreed consent IDs
        agreedConsents: [ApplicantAgreementSettings!]!
    ): ApplicationJourney!

    """
    Guarantor provides KYC
    """
    submitGuarantorKYC(token: String!, fields: [LocalCustomerFieldSettings!]!, saveDraft: Boolean): ApplicationJourney!

    """
    Guarantor agrees on CnD
    """
    submitGuarantorAgreements(
        # journey token
        token: String!

        # agreed consent IDs
        agreedConsents: [ApplicantAgreementSettings!]!
    ): ApplicationJourney!

    """
    Applicant set appointment
    """
    submitApplicantAppointment(token: String!, bookingTimeSlot: SubmitAppointmentBookingTimeSlot): ApplicationJourney!

    """
    Update appointment time slot
    """
    updateApplicantAppointment(token: String!, bookingTimeSlot: SubmitAppointmentBookingTimeSlot): Boolean!

    """
    Applicant set visit appointment
    """
    submitApplicantVisitAppointment(
        token: String!
        bookingTimeSlot: SubmitAppointmentBookingTimeSlot
    ): ApplicationJourney!

    """
    Update visit appointment time slot
    """
    updateApplicantVisitAppointment(token: String!, bookingTimeSlot: SubmitAppointmentBookingTimeSlot): Boolean!

    """
    Update or Assign assignee to Application
    """
    updateAssigneeOnApplication(
        applicationId: ObjectID!
        assigneeId: ObjectID
        stage: ApplicationStage!
        languageId: ObjectID
    ): Application

    """
    Share Application
    """
    shareStandardApplication(
        # module application ID
        moduleId: ObjectID!
        # customer info fullName&email
        customer: ApplicationCustomerDraft!
        # Application configuration
        configuration: StandardApplicationConfiguration!
        # financing settings
        financing: [ApplicationFinanceSettings!]!
        # insurancing settings
        insurancing: [ApplicationInsuranceSettings!]!
        # vehicle draft
        vehicle: [ApplicationVehicleDraft!]!
        # dealer Id
        dealerId: ObjectID!
        # endpoint Id
        endpointId: ObjectID!
        # agreed consent IDs
        agreedConsents: [ApplicantAgreementSettings!]!
        # promo code ID
        promoCodeId: ObjectID
        # language ID
        languageId: ObjectID
    ): Application!

    """
    Draft a new event application
    """
    draftEventApplication(
        # module application ID
        moduleId: ObjectID!
        # customer/applicant draft
        customer: ApplicationCustomerDraft!
        # vehicle draft
        vehicle: ApplicationVehicleDraft!
        # endpoint ID
        eventId: ObjectID!
        # event application configuration
        configuration: EventApplicationConfigurationPayload!
        # event application configuration
        tradeInVehicle: [TradeInVehiclePayload!]!
        # endpoint ID
        endpointId: ObjectID
        # dealer id
        dealerId: ObjectID!
        # language ID
        languageId: ObjectID
        # Customer Kind
        customerKind: CustomerKind!
        # User Customized Field Input
        customizedFields: [EventCustomizedFieldInput!]!
        # Remarks
        remarks: String
        # Is draft created when click myinfo
        withMyinfo: Boolean
        # Is draft created when click Porsche ID
        withPorscheId: Boolean
        # Applicant has C@P Values
        capValues: CapValuesInput
        # Campaign values
        campaignValues: ApplicationCampaignValuesInput
        originSalesConsultantId: ObjectID
    ): ApplicationJourney!

    """
    update event application
    """
    updateEventApplication(
        # updateEventApplication token
        token: String!
        # dealer
        dealerId: ObjectID!
        # vehicle draft
        vehicle: ApplicationVehicleDraft!
        # event application configuration
        configuration: EventApplicationConfigurationPayload!
        # event application configuration
        tradeInVehicle: [TradeInVehiclePayload!]!
        # Customer Kind
        customerKind: CustomerKind!
        # Customized fields
        customizedFields: [EventCustomizedFieldInput!]!
        # Remarks
        remarks: String
        # Applicant has C@P Values
        capValues: CapValuesInput
    ): ApplicationJourney!

    """
    Submit Payment Application
    """
    submitAdyenPayment(
        token: String!
        agreedConsents: [ApplicantAgreementSettings!]!
        skipDeposit: Boolean!

        # Session data can be empty when skip deposit is true
        sessionResult: String
    ): ApplicationJourney!

    """
    Generate OTP
    """
    generateSigningOTP(token: String!, purpose: ApplicationSigningPurpose!): GeneratedOTPResult!

    """
    Validate OTP
    """
    submitSigningOTP(token: String!, code: String!, purpose: ApplicationSigningPurpose!): ApplicationJourney!

    """
    Upload application document
    """
    uploadApplicationDocuments(token: String!, payload: [ApplicationDocumentPayload!]!): Boolean

    """
    Draft a new configurator application
    """
    draftConfiguratorApplication(
        # module application ID
        moduleId: ObjectID!
        # customer/applicant draft
        customer: ApplicationCustomerDraft!
        # vehicle draft
        vehicle: ApplicationVehicleDraft!
        # configurator ID
        configuratorId: ObjectID!
        # configurator application configuration
        configuration: ConfiguratorApplicationConfigurationPayload!
        # financing settings
        financing: ApplicationFinanceSettings
        # insurancing settings
        insurancing: ApplicationInsuranceSettings
        # configurator blocks
        configuratorBlocks: [ConfiguratorBlockPayload!]!
        # trade in vehicle
        tradeInVehicle: [TradeInVehiclePayload!]!
        # endpoint ID
        endpointId: ObjectID
        # dealer ID
        dealerId: ObjectID!
        # send email
        isDraft: Boolean
        # promo code ID
        promoCodeId: ObjectID

        # language ID
        languageId: ObjectID
    ): ApplicationJourney!

    """
    Save the Configurator Order
    """
    updateConfiguratorApplication(
        token: String!
        # financing settings
        financing: ApplicationFinanceSettings
        # insurancing settings
        insurancing: ApplicationInsuranceSettings
        # configurator application configuration
        configuration: ConfiguratorApplicationConfigurationPayload!
        # customer/applicant draft
        customer: ApplicationCustomerDraft!
        # dealer ID
        dealerId: ObjectID!
        # send email
        isDraft: Boolean
        # vehicle draft
        vehicle: ApplicationVehicleDraft!
        #configurator setting
        configuratorBlocks: [ConfiguratorBlockPayload!]
    ): ApplicationJourney!

    """
    Update Configurator Application Journey
    """
    updateConfiguratorApplicationJourney(
        # updateConfiguratorApplication token
        token: String!
        # event application configuration
        configuration: ConfiguratorApplicationConfigurationPayload!
        # event application configuration
        tradeInVehicle: [TradeInVehiclePayload!]!
        # Customer Kind
        customerKind: CustomerKind!
        financing: UpdateApplicationJourneyFinancingSettings
    ): ApplicationJourney!

    """
    Update Configurator Application Configuration
    """
    updateConfiguratorApplicationConfiguration(
        token: String!
        configuration: ConfiguratorApplicationConfigurationPayload!
    ): Boolean!

    """
    Extend configurator stock expriy
    """
    extendConfiguratorStockExpiry(
        # application token
        token: String!
    ): ApplicationJourney!

    """
    Extend mobility stock expriy
    """
    extendMobilityStockExpiry(
        # application token
        token: String!
    ): ApplicationJourney!

    """
    Release reserved configurator stock
    """
    releaseReservedConfiguratorStock(
        # application token
        token: String!
    ): Boolean!

    """
    Release reserved mobility stock
    """
    releaseReservedMobilityStock(
        # application token
        token: String!
    ): Boolean!

    """
    Resubmit Application to all kind of application
    """
    submitChanges(
        applicationId: ObjectID!
        updates: [ApplicationUpdate!]
        withRedirectionLink: Boolean
        stage: ApplicationStage!
        remarks: String
        commentsToInsurer: String
    ): ApplicationJourney!

    updateApplication(
        applicationId: ObjectID!
        updates: [ApplicationUpdate!]
        stage: ApplicationStage!
        remarks: String
        commentsToInsurer: String
    ): Application!

    updateMobilityApplication(
        applicationId: ObjectID!
        updates: [ApplicationUpdate!]
        stage: ApplicationStage!
    ): UpdatedMobilityApplication!

    """
    Send email to customer device to proceed
    """
    proceedWithCustomerDevice(token: String, fields: [LocalCustomerFieldSettings!]!): Boolean!

    """
    Update Application Fields
    """
    updateApplicationFields(token: String!, remarks: String!, commentsToInsurer: String): Boolean!

    """
    Approves an application
    """
    approveApplication(applicationId: ObjectID!, stage: ApplicationStage!): Boolean!

    """
    Declines an application
    """
    declineApplication(applicationId: ObjectID!, stage: ApplicationStage!): Boolean!

    """
    Contact the customer
    """
    contactApplication(applicationId: ObjectID!, stage: ApplicationStage!): Boolean!

    """
    Check In an application (appointment)
    """
    checkInApplication(applicationId: ObjectID!, stage: ApplicationStage!): Boolean!

    """
    Confirm test drive appointment booking
    """
    confirmBookingApplication(
        applicationId: ObjectID!
        stage: ApplicationStage!
        capValues: CapValuesInput
        appointmentDetails: ApplicationUpdateAppointmentDetails
        updatedKyc: [LocalCustomerFieldSettings!]
        updatedTradeInVehicle: [TradeInVehiclePayload!]
        qualifyValues: QualifyLeadInput
    ): Boolean!

    """
    Cancels an application
    """
    cancelApplication(applicationId: ObjectID, token: String, stage: ApplicationStage!): Boolean!

    """
    Complete an application
    """
    completeApplication(applicationId: ObjectID!, stage: ApplicationStage!): Boolean!

    """
    Concludes the agreement for the finder application
    """
    concludeAgreementApplication(applicationId: ObjectID!, stage: ApplicationStage!): Boolean!

    """
    Refresh application status
    """
    refreshApplicationStatus(applicationId: ObjectID!): Boolean!

    """
    Generate Passcode for Remote Journey
    """
    generateRemoteJourneyPasscode(secret: String!): GeneratedOTPResult!

    """
    Validate Passcode for Remote Journey
    """
    validateRemoteJourneyPasscode(secret: String!, code: String!): ApplicationJourney!

    """
    Validate Passcode for Sales Offer Remote Journey to return token
    """
    validateSalesOfferRemoteJourneyPasscode(secret: String!, code: String!): String!

    """
    Generate sales offer journey token
    """
    generateSalesOfferJourneyToken(secret: String!): String!

    """
    Upload application document
    """
    uploadApplicationDocument(
        applicationId: ObjectID
        token: String
        upload: Upload!
        kind: ApplicationDocumentKind!
        isTemporary: Boolean
    ): UploadApplicationDocumentResult!

    """
    Delete application document
    """
    deleteApplicationDocument(applicationId: ObjectID, token: String, uploadId: ObjectID!): Application

    """
    Request Release Letter
    """
    requestReleaseLetter(applicationId: ObjectID!, settings: RequestReleaseLetterSettings!): Application

    """
    Request Disbursement
    """
    requestDisbursement(applicationId: ObjectID!): Application

    """
    Submit Porsche Payment Application
    """
    submitPorschePayment(
        token: String!
        agreedConsents: [ApplicantAgreementSettings!]!
        skipDeposit: Boolean!
    ): ApplicationJourney!

    """
    Submit Porsche Payment for sales offer application
    """
    submitSalesOfferPorschePayment(token: String!, agreedConsents: [ApplicantAgreementSettings!]!): SalesOfferJourney!

    """
    Submit Fiserv Payment Application
    """
    submitFiservPayment(
        token: String!
        agreedConsents: [ApplicantAgreementSettings!]!
        skipDeposit: Boolean!
    ): ApplicationJourney!

    """
    Submit PayGate Payment Application
    """
    submitPayGatePayment(
        token: String!
        agreedConsents: [ApplicantAgreementSettings!]!
        skipDeposit: Boolean!
    ): ApplicationJourney!

    """
    Submit TTB Payment Application
    """
    submitTtbPayment(
        token: String!
        agreedConsents: [ApplicantAgreementSettings!]!
        skipDeposit: Boolean!
    ): ApplicationJourney!

    """
    Draft a new mobility application
    """
    draftMobilityApplication(
        # mobility module ID
        moduleId: ObjectID!
        # customer/applicant draft
        customer: ApplicationCustomerDraft!
        # mobility details
        mobilityDetails: [MobilityDetailsPayload!]!
        # mobility booking details
        mobilityBookingDetails: MobilityBookingDetailsPayload!
        # endpoint ID
        endpointId: ObjectID

        # language ID
        languageId: ObjectID
    ): ApplicationJourney!

    """
    Update curent mobility application draft
    """
    updateMobilityApplicationDraft(
        token: String!
        mobilityDetails: [MobilityDetailsPayload!]!
        mobilityBookingDetails: MobilityBookingDetailsPayload!
    ): ApplicationJourney!

    """
    Generate amend mobility application access
    """
    generateAmendMobilityApplicationAccess(
        applicationId: ObjectID!
        period: PeriodPayload!
    ): AmendMobilityApplicationAccess

    """
    Generate cancel mobility application access
    """
    generateCancelMobilityApplicationAccess(applicationId: ObjectID!): CancelMobilityApplicationAccess

    """
    Amend mobility application
    """
    amendMobilityApplication(
        token: String!
        period: PeriodPayload!
        location: MobilityBookingLocationPayload!
    ): ApplicationJourney

    """
    Draft a new finder application
    """
    draftFinderApplication(
        # module application ID
        moduleId: ObjectID!
        # customer/applicant draft
        customer: ApplicationCustomerDraft!
        # vehicle draft
        vehicle: ApplicationVehicleDraft!
        # financing settings
        financing: ApplicationFinanceSettings
        # insurancing settings
        insurancing: ApplicationInsuranceSettings
        # event application configuration
        configuration: FinderApplicationConfigurationPayload!
        # event application configuration
        tradeInVehicle: [TradeInVehiclePayload!]!
        # endpoint ID
        endpointId: ObjectID
        promoCodeId: ObjectID
        # dealer id
        dealerId: ObjectID!
        # language ID
        languageId: ObjectID

        isTestDriveDrafting: Boolean
    ): ApplicationJourney!

    """
    Draft a new finder application
    """
    draftFinderApplicationFromLead(
        # lead ID
        leadId: ObjectID!
        # module application ID
        moduleId: ObjectID!
        # vehicle draft
        vehicle: ApplicationVehicleDraft!
        # financing settings
        financing: ApplicationFinanceSettings
        # insurancing settings
        insurancing: ApplicationInsuranceSettings
        # event application configuration
        configuration: FinderApplicationConfigurationPayload!
        # event application configuration
        tradeInVehicle: [TradeInVehiclePayload!]!
        # endpoint ID
        endpointId: ObjectID
        # promo code ID
        promoCodeId: ObjectID
        # dealer id
        dealerId: ObjectID!
        # language ID
        languageId: ObjectID
        isTestDriveDrafting: Boolean
    ): ApplicationJourney!

    """
    update existing finder application draft
    """
    updateFinderApplicationDraft(
        token: String!
        # customer/applicant draft
        customer: ApplicationCustomerDraft!
        # event application configuration
        configuration: FinderApplicationConfigurationPayload!
        # event application configuration
        tradeInVehicle: [TradeInVehiclePayload!]!
        # financing settings
        financing: ApplicationFinanceSettings
        # insurancing settings
        insurancing: ApplicationInsuranceSettings
        promoCodeId: ObjectID
        # dealer id
        dealerId: ObjectID!
        # language ID
        languageId: ObjectID
        isTestDriveDrafting: Boolean
    ): ApplicationJourney!

    """
    Update Finder application
    """
    updateFinderApplication(
        # token
        token: String!
        # financing settings
        financing: ApplicationFinanceSettings
        # insurancing settings
        insurancing: ApplicationInsuranceSettings
        # event application configuration
        configuration: FinderApplicationConfigurationPayload!
        # event application configuration
        tradeInVehicle: [TradeInVehiclePayload!]!
        promoCodeId: ObjectID
        # language ID
        languageId: ObjectID
        isTestDriveDrafting: Boolean
    ): ApplicationJourney!

    """
    Update finder application journey

    For trade in vehicle
    """
    updateFinderApplicationJourney(
        # application token
        token: String!
        # inputted trade in vehicle
        tradeInVehicle: [TradeInVehiclePayload!]!
        # Customer Kind
        customerKind: CustomerKind!
        financing: UpdateApplicationJourneyFinancingSettings
        saveDraft: Boolean
    ): ApplicationJourney!

    """
    Extend finder vehicle expiration time
    """
    extendFinderVehicleExpiry(
        # application token
        token: String!
    ): ApplicationJourney!

    """
    Release finder vehicle expiration time
    """
    releaseFinderVehicleExpiry(
        # application token
        token: String!
    ): Boolean!

    """
    Add Comment to Audit Trail
    """
    addAuditTrailComment(applicationId: ObjectID!, comment: String!, applicationStage: ApplicationStage!): Boolean!

    """
    Extend event journey expiry
    """
    extendEventJourneyExpiry(
        # application token
        token: String!
    ): ApplicationJourney!

    """
    Get application agreement and KYC fields from journey

    This used for retrieving agreements and KYC fields, which configuration is updated inside KYC page
    Ie: Test drive checkbox and trade in checkbox inside KYC page Standard and Configurator application
    For event application, it use different handler, since the initial page is KYC page
    """
    getAgreementsAndKycFieldsFromUpdatedConfiguration(
        token: String!
        configuration: UpdatedConfigurationSettings!
    ): Application!

    """
    Submit application quotation data during journey

    Applicable to application with financing and ENBD bank integration
    """
    submitApplicationQuotation(token: String!, quotation: ApplicationQuotationSettings!): ApplicationJourney!

    """
    Updates deposit amount for mobility since promo code allows deduction
    of payment price in this journey
    """
    updateMobilityDepositAmount(token: String!, promoCodeId: ObjectID, giftVoucherId: ObjectID): ApplicationJourney!

    """
    Update test drive data for appointment

    This is used, because we don't want to trigger submission changes for test drive data change
    """
    updateTestDriveData(applicationId: ObjectID!, testDriveData: ApplicationUpdateTestDriveDetails!): Boolean!
    """
    Update solely appointment data

    This is used, because we don't want to trigger submission changes for appointment data change
    """
    updateAppointmentData(
        applicationId: ObjectID!
        appointmentData: ApplicationUpdateAppointmentDateTimeData!
        stage: ApplicationStage!
    ): Boolean!

    """
    Start test drive for appointment

    Origin endpoint id is where this action is triggered
    For CI, it's should be endpoint id of appointment details
    For Admin, it's null
    """
    startTestDrive(applicationId: ObjectID!, testDriveRedirectUrl: String): ApplicationJourney!

    """
    End test drive for appointment
    """
    endTestDrive(applicationId: ObjectID!): Boolean!

    """
    Unqualify the application for C@P submission
    """
    unqualifyApplication(applicationId: ObjectID!): Boolean!

    """
    Apply new function
    """
    applyNew(applicationId: ObjectID!, stage: ApplicationStage!): String!

    """
    Apply new application for showroom
    """
    applyNewStandardApplication(
        # application ID
        applicationId: ObjectID!
        # module application ID
        moduleId: ObjectID!
        # Application configuration
        configuration: StandardApplicationConfiguration!
        # financing settings
        financing: ApplicationFinanceSettings!
        # insurancing settings
        insurancing: ApplicationInsuranceSettings
    ): ApplicationJourney!

    """
    Continue application
    """
    continueApplication(
        applicationId: ObjectID!
        stage: ApplicationStage!
        isProceedWithCustomerDevice: Boolean
        isSkipDeposit: Boolean
        continueRequestFinancing: Boolean
    ): String

    """
    Apply new application for finder
    """
    applyNewFinderApplication(
        # application ID
        applicationId: ObjectID!
        # module application ID
        moduleId: ObjectID!
        # Application configuration
        configuration: FinderApplicationConfigurationPayload!
        # financing settings
        financing: ApplicationFinanceSettings!
        # insurancing settings
        insurancing: ApplicationInsuranceSettings
    ): ApplicationJourney!

    """
    Apply new application for configurator
    """
    applyNewConfiguratorApplication(
        # application ID
        applicationId: ObjectID!
        # module application ID
        moduleId: ObjectID!
        # Application configuration
        configuration: ConfiguratorApplicationConfigurationPayload!
        # financing settings
        financing: ApplicationFinanceSettings!
        # insurancing settings
        insurancing: ApplicationInsuranceSettings
    ): ApplicationJourney!

    """
    """
    createAppointmentFromLead(
        # Lead Id
        leadId: ObjectID!
        bookingTimeSlot: SubmitAppointmentBookingTimeSlot!
        vehicleId: ObjectID!
        # customer/applicant draft
        fields: [LocalCustomerFieldSettings!]!
        # trade in vehicle
        tradeInVehicle: [TradeInVehiclePayload!]!
        # agreed consent IDs
        agreedConsents: [ApplicantAgreementSettings!]!
        # endpoint ID
        endpointId: ObjectID
        # language ID
        languageId: ObjectID
    ): Application!

    """
    Create Showroom Visit Appointment From Lead (Launchpad)
    """
    createShowroomVisitAppointmentFromLead(
        # Lead Id
        leadId: ObjectID!
        bookingTimeSlot: SubmitAppointmentBookingTimeSlot!
        # agreed consent IDs
        agreedConsents: [ApplicantAgreementSettings!]!
        # endpoint ID
        endpointId: ObjectID
        # language ID
        languageId: ObjectID
    ): Application!

    """
    Draft LaunchPad application
    """
    draftLaunchpadApplication(
        # Lead ID
        leadId: ObjectID!
        # Launchpad ModuleID
        launchpadModuleId: ObjectID!
        # trade in vehicle
        tradeInVehicle: [TradeInVehiclePayload!]!

        # endpoint ID
        endpointId: ObjectID
        # language ID
        languageId: ObjectID
    ): Application!

    """
    Draft LaunchPad application
    """
    updateLaunchpadApplicationTradeIn(
        # Application ID
        applicationId: ObjectID!
        # trade in vehicle
        tradeInVehicle: [TradeInVehiclePayload!]!
    ): Application!
}
