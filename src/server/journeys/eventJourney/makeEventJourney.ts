import { isNil } from 'lodash/fp';
import {
    ApplicationScenario,
    Event,
    EventApplication,
    EventApplicationModule,
    SettingId,
} from '../../database/documents';
import { getDealershipPaymentSetting, getDealershipPaymentSettingId } from '../../utils/getDealershipPaymentSetting';
import JourneyContext from '../JourneyContext';
import JourneyStep from '../JourneyStep';
import ApplicantAgreementsStep from '../common/ApplicantAgreementsStep';
import ApplicantKYCStep from '../common/ApplicantKYCStep';
import AppointmentStep from '../common/AppointmentStep';
import DraftingStep from '../common/DraftingStep';
import GuarantorAgreementsStep from '../common/GuarantorAgreementsStep';
import GuarantorKYCStep from '../common/GuarantorKYCStep';
import SystemReceivalStep from '../common/SystemReceivalStep';
import VisitAppointmentStep from '../common/VisitAppointmentStep';
import { hasAppointmentScenario, hasPaymentScenario } from '../common/helpers';
import EventAdyenPaymentStep from './EventAdyenPaymentStep';
import EventFiservPaymentStep from './EventFiservPaymentStep';
import EventJourneyContext from './EventJourneyContext';
import EventPayGatePaymentStep from './EventPayGatePaymentStep';
import EventPorschePaymentStep from './EventPorschePaymentStep';
import EventTtbPaymentStep from './EventTtbPaymentStep';

const getDepositStep = async (context: EventJourneyContext) => {
    const { loaders, event, application } = context;

    const dealer = await loaders.dealerById.load(application.dealerId);
    const setting = await getDealershipPaymentSetting(dealer._id, event, { loaders });

    if (!setting) {
        throw new Error('Setting not found');
    }

    switch (setting.settingId) {
        case SettingId.AdyenPayment:
            return new EventAdyenPaymentStep(context);

        case SettingId.PorschePayment: {
            if (!dealer.integrationDetails.assortment) {
                return null;
            }

            return new EventPorschePaymentStep(context);
        }

        case SettingId.FiservPayment:
            return new EventFiservPaymentStep(context);

        case SettingId.PayGatePayment:
            return new EventPayGatePaymentStep(context);

        case SettingId.TtbPayment:
            return new EventTtbPaymentStep(context);

        default:
            throw Error('Invalid Setting Type');
    }
};

const appendAppointmentSteps = (
    event: Event,
    context: EventJourneyContext,
    journey: JourneyStep<JourneyContext<EventApplication, EventApplicationModule>, any>,
    application?: EventApplication
): JourneyStep<JourneyContext<EventApplication, EventApplicationModule>, any> => {
    let updatedJourney = journey;
    if (event.scenarios.includes(ApplicationScenario.Appointment)) {
        const hasStep =
            !isNil(context.applicationModule.appointmentModuleId) &&
            context.application.configuration.testDrive &&
            // need to check if scenario is covered for this event
            hasAppointmentScenario(context.event.scenarios);

        if (hasStep && application?.configuration?.testDrive) {
            updatedJourney = updatedJourney.append(new AppointmentStep(context));
        }
    }
    if (
        event.scenarios.includes(ApplicationScenario.VisitAppointment) &&
        application?.configuration?.visitAppointment
    ) {
        updatedJourney = updatedJourney.append(new VisitAppointmentStep(context));
    }

    return updatedJourney;
};

const makeEventJourney = async (context: EventJourneyContext) => {
    const { event, journey: journeyState, includeWholeJourney, application } = context;
    const isResubmit = !isNil(journeyState.submission);
    const hasPayment = hasPaymentScenario(event.scenarios);

    const hasGuarantor = journeyState?.hasGuarantorCustomer;

    let journey = new DraftingStep(context)
        .append(new ApplicantAgreementsStep(context))
        .append(new ApplicantKYCStep(context));

    journey = appendAppointmentSteps(event, context, journey, application);

    if (hasGuarantor) {
        journey = journey.append(new GuarantorAgreementsStep(context));
    }

    if (hasGuarantor && (!journeyState.guarantorKYC?.completed || includeWholeJourney)) {
        journey = journey.append(new GuarantorKYCStep(context));
    }

    if (
        (!journeyState.deposit?.completed || includeWholeJourney) &&
        (!journeyState.deposit?.skipped || includeWholeJourney) &&
        hasPayment &&
        !isResubmit &&
        !isNil(getDealershipPaymentSettingId(application.dealerId, event))
    ) {
        const depositStep = await getDepositStep(context);

        if (depositStep) {
            journey = journey.append(depositStep);
        }
    }

    journey = journey.append(new SystemReceivalStep(context));

    return journey.first;
};

export default makeEventJourney;
