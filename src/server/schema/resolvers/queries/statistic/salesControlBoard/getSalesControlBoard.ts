import dayjs from 'dayjs';
import { PeriodPayload } from '../../../../../../app/api';
import {
    LocalModel,
    ModuleType,
    SalesControlBoardModule,
    SimpleVehicleManagementModule,
    User,
    VehicleKind,
} from '../../../../../database/documents';
import getDatabaseContext from '../../../../../database/getDatabaseContext';
import { DealerPolicyAction } from '../../../../../permissions';
import ensureManyFromLoaders from '../../../../../utils/ensureManyFromLoaders';
import { InvalidPermission } from '../../../../errors';
import { requiresLoggedUser } from '../../../../middlewares';
import { GraphQLQueryResolvers } from '../../../definitions';
import {
    getFICommissions,
    getWeekFunnels,
    getProgressGoal,
    getSalesControlBoardSettingFromDealer,
    getModelAverageTarget,
    getSalesPerformanceOverview,
} from './helpers';

const query: GraphQLQueryResolvers['getSalesControlBoard'] = async (
    root,
    { filter },
    { loaders, getPermissionController, getUser }
) => {
    const { collections } = await getDatabaseContext();
    const permissionController = await getPermissionController();

    const { dealerId, monthOfImport, isYtd, salesConsultant, vehicleModel } = filter;
    const dealer = await loaders.dealerById.load(dealerId);

    if (!dealer) {
        throw new Error('Dealer not found');
    }

    const hasManagerPermission = await permissionController.dealers.mayOperateOn(
        dealer,
        DealerPolicyAction.ViewSalesControlBoardManager
    );

    const hasConsultantPermission = await permissionController.dealers.mayOperateOn(
        dealer,
        DealerPolicyAction.ViewSalesControlBoardSalesConsultant
    );

    if (!hasManagerPermission && !hasConsultantPermission) {
        throw new InvalidPermission();
    }

    const salesControlBoardModule = (await collections.modules.findOne({
        companyId: dealer.companyId,
        _type: ModuleType.SalesControlBoardModule,
    })) as SalesControlBoardModule;

    if (!salesControlBoardModule) {
        throw new Error('Sales control board module not found');
    }

    const user = await getUser();

    const convertedPeriod: PeriodPayload = isYtd
        ? { start: dayjs(monthOfImport).startOf('year').toDate(), end: dayjs(monthOfImport).endOf('month').toDate() }
        : {
              start: dayjs(monthOfImport).startOf('month').toDate(),
              end: dayjs(monthOfImport).endOf('month').toDate(),
          };

    const selectedSalesConsultantIds = (() => {
        const allSalesConsultantIds = salesControlBoardModule.salesConsultantsAssignments.overrides.find(
            ({ dealerId: overrideDealerId }) => overrideDealerId?.equals(dealerId)
        ).value;

        // if hasManagerPermission and filter parameter 'salesConsultant' is not provided, return all sales consultants
        if (hasManagerPermission && salesConsultant.length === 0) {
            return allSalesConsultantIds;
        }

        // eslint-disable-next-line max-len
        // if hasManagerPermission and filter parameter 'salesConsultant' is provided, return only those sales consultants
        if (hasManagerPermission && salesConsultant.length > 0) {
            return salesConsultant.filter(id => allSalesConsultantIds.find(i => i.equals(id)));
        }

        // if hasConsultantPermission and user is in dealer sales consultants, return only that user
        if (hasConsultantPermission && allSalesConsultantIds.find(i => i.equals(user._id))) {
            return [user._id];
        }

        return [];
    })();

    const selectedSalesConsultants = await loaders.userById
        .loadMany(selectedSalesConsultantIds)
        .then(ensureManyFromLoaders<User>);

    const vehicleManagementModule = (await collections.modules.findOne({
        companyId: dealer.companyId,
        _type: ModuleType.SimpleVehicleManagement,
    })) as SimpleVehicleManagementModule;

    const vehicleModels = await collections.vehicles
        .find<LocalModel>({
            _kind: VehicleKind.LocalModel,
            moduleId: vehicleManagementModule._id,
            '_versioning.isLatest': true,
            isDeleted: false,
            isActive: true,
            $or: [{ parentModelId: { $exists: false } }, { parentModelId: null }],
        })
        .toArray();

    const selectedVehicleModels =
        vehicleModel.length > 0 ? vehicleModel.map(id => vehicleModels.find(i => i._id.equals(id))) : vehicleModels;
    const selectedVehicleModelIds = selectedVehicleModels.map(model => model._id);

    const salesControlBoardSetting = getSalesControlBoardSettingFromDealer(dealerId, salesControlBoardModule);
    const modelAverageTarget = getModelAverageTarget(vehicleModels.length, salesControlBoardSetting);

    const fiCommissions = await getFICommissions({
        dealerId,
        salesControlBoardSetting,
        hasManagerPermission,
        monthOfImport,
        selectedSalesConsultants,
        selectedVehicleModelIds,
        collections,
    });

    const progressGoal = await getProgressGoal({
        dealerId,
        salesControlBoardSetting,
        modelAverageTarget,
        hasManagerPermission,
        hasConsultantPermission,
        monthOfImport,
        collections,
        user,
        selectedSalesConsultantIds,
        selectedVehicleModelIds,
        isYtd,
    });

    const weekFunnels = getWeekFunnels(monthOfImport, hasManagerPermission);

    const performanceOverview = await getSalesPerformanceOverview({
        dealerId,
        monthOfImport,
        collections,
        convertedPeriod,
        selectedSalesConsultantIds,
        selectedVehicleModelIds,
        isYtd,
    });

    return {
        fiCommissions,
        weekFunnels,
        progressGoal,
        performanceOverview,
    };
};

export default requiresLoggedUser(query);
