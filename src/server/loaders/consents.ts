import DataLoader from 'dataloader';
import { ObjectId } from 'mongodb';
import { ConsentsAndDeclarations } from '../database/documents';
import getDatabaseContext from '../database/getDatabaseContext';
import { buildManyToManyLoader, buildOneToOneLoader } from './helpers';

export type ConsentLoaders = {
    consentById: DataLoader<ObjectId, ConsentsAndDeclarations>;
    consentsAndDeclarationsByModuleId: DataLoader<ObjectId, ConsentsAndDeclarations[]>;
    consentsAndDeclarationsByParentId: DataLoader<ObjectId, ConsentsAndDeclarations[]>;
};

const createConsentLoaders = (): ConsentLoaders => {
    const consentById = buildOneToOneLoader<ConsentsAndDeclarations>(keys =>
        getDatabaseContext().then(({ collections }) =>
            collections.consentsAndDeclarations.find({ _id: { $in: keys } }).toArray()
        )
    );

    const consentsAndDeclarationsByModuleId = buildManyToManyLoader<ConsentsAndDeclarations>(
        keys =>
            getDatabaseContext().then(({ collections }) =>
                collections.consentsAndDeclarations.find({ moduleId: { $in: keys } }).toArray()
            ),
        document => [document.moduleId.toHexString()]
    );

    const consentsAndDeclarationsByParentId = new DataLoader<ObjectId, ConsentsAndDeclarations[]>(async keys => {
        const documents = await getDatabaseContext().then(({ collections }) =>
            collections.consentsAndDeclarations
                .find({
                    parentId: { $in: keys },
                    isDeleted: false,
                    '_versioning.isLatest': true,
                })
                .sort({ orderNumber: 1 })
                .toArray()
        );

        // Group documents by parentId using the same logic as buildManyToManyLoader
        const mappedDocuments = documents.reduce(
            (acc, document) => {
                if (document.parentId) {
                    const parentKey = document.parentId.toHexString();
                    if (!acc[parentKey]) {
                        acc[parentKey] = [];
                    }
                    acc[parentKey].push(document);
                }

                return acc;
            },
            {} as Record<string, ConsentsAndDeclarations[]>
        );

        // Sort each group by orderNumber to ensure correct order
        Object.values(mappedDocuments).forEach(group => {
            group.sort((a, b) => (a.orderNumber || 0) - (b.orderNumber || 0));
        });

        return keys.map(key => mappedDocuments[key.toHexString()] || []);
    });

    return { consentById, consentsAndDeclarationsByModuleId, consentsAndDeclarationsByParentId };
};

export default createConsentLoaders;
