import { useCallback, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'react-router-dom';
import styled from 'styled-components';
import { useGetDataFromPorscheIdQuery } from '../../api/queries/getDataFromPorscheId';
import { useThemeComponents } from '../../themes/hooks';
import { PorscheIDLoginType } from './types';
import useFetchPorscheIdAuthorizedUrl from './useFetchPorscheIdAuthorizedUrl';

const InfoContainer = styled.div`
    background-color: #eff0f1;
    border-radius: 6px;
    padding: 1.5rem;
    font-weight: 400;
    font-size: 1rem;
`;
const ButtonContainer = styled.span`
    margin-top: 2rem;
    display: flex;
    gap: 10px;
`;

const MandatoryPorscheIDLogin = ({
    applicationId,
    routerId,
    endpointId,
    onPorscheIDCustomerFetched,
    setIsPorscheIDFetchLoading,
    submitDraft,
    isEvent = false,
    persistValueFn,
}: PorscheIDLoginType) => {
    const { t } = useTranslation(['porscheIdLoginRegister', 'eventApplicantForm']);
    const { Button, notification } = useThemeComponents();
    const fetchPorscheIdAuthorizedUrl = useFetchPorscheIdAuthorizedUrl();
    const location = useLocation();
    const { state: routerState } = location;

    const { data, loading } = useGetDataFromPorscheIdQuery({
        fetchPolicy: 'cache-and-network',
        variables: { applicationId, code: routerState?.porscheIdAuthorizationCode, linkId: routerState?.linkId },
        skip: !applicationId || !routerState?.porscheIdAuthorizationCode || !routerState?.linkId,
    });

    const redirectToPorscheIDPortal = useCallback(async () => {
        if (persistValueFn) {
            persistValueFn();
        }

        if (isEvent && !applicationId) {
            notification.loading({
                content: t('eventApplicantForm:messages.creationSubmitting'),
                duration: 0,
                key: 'primary',
            });

            const newApplicationId = await submitDraft();
            if (newApplicationId) {
                fetchPorscheIdAuthorizedUrl.requestForAuthorizedUrl({
                    applicationId: newApplicationId,
                    routerId,
                    endpointId,
                });
            }
        } else {
            fetchPorscheIdAuthorizedUrl.requestForAuthorizedUrl({ applicationId, routerId, endpointId });
        }
    }, [
        applicationId,
        endpointId,
        fetchPorscheIdAuthorizedUrl,
        isEvent,
        notification,
        persistValueFn,
        routerId,
        submitDraft,
        t,
    ]);

    useEffect(() => {
        setIsPorscheIDFetchLoading(loading);
    }, [loading, setIsPorscheIDFetchLoading]);

    useEffect(() => {
        if (!loading && data?.porscheIdData && onPorscheIDCustomerFetched) {
            onPorscheIDCustomerFetched(data.porscheIdData);
        }
    }, [data, loading, onPorscheIDCustomerFetched]);

    return (
        <>
            <InfoContainer>{t('porscheIdLoginRegister:info')}</InfoContainer>
            <ButtonContainer>
                <Button onClick={redirectToPorscheIDPortal} type="primary">
                    {t('porscheIdLoginRegister:button.login')}
                </Button>
                <Button onClick={redirectToPorscheIDPortal} type="primary">
                    {t('porscheIdLoginRegister:button.register')}
                </Button>
            </ButtonContainer>
        </>
    );
};

export default MandatoryPorscheIDLogin;
