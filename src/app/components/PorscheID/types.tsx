import { Dispatch, SetStateAction } from 'react';
import { GetPorscheIdAuthorizeUrlQueryVariables } from '../../api/queries/getPorscheIdAuthorizeUrl';
import { PorscheIdData } from '../../api/types';

export type PorscheIDLoginType = GetPorscheIdAuthorizeUrlQueryVariables & {
    onPorscheIDCustomerFetched?: (porscheIdData: PorscheIdData) => void;
    setIsPorscheIDFetchLoading?: Dispatch<SetStateAction<Boolean>>;
    submitDraft?: () => Promise<string | null>;
    isEvent?: boolean;
    persistValueFn?: () => void;
};
