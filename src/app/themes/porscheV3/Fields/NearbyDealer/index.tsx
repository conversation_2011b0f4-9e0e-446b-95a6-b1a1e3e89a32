import { components<PERSON><PERSON>y, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, PTag } from '@porsche-design-system/components-react';
import { useField } from 'formik';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { type JourneyEventDataFragment } from '../../../../api/fragments/JourneyEventData';
import { type NearbyDealersDataFragment } from '../../../../api/fragments/NearbyDealersData';
import { useGetNearbyDealersQuery } from '../../../../api/queries/getNearbyDealers';
import { type NearbyDealerFilteringRule } from '../../../../api/types';
import { useCompany } from '../../../../components/contexts/CompanyContextManager';
import FormItem from '../../../../components/fields/FormItem';
import useTranslatedString from '../../../../utilities/useTranslatedString';
import DealerInfo from './DealerInfo';

const StyledDiv = styled.div`
    position: relative;
`;

const StyledPTextField = styled(PTextFieldWrapper)`
    position: relative;
    z-index: 1;

    &:has(input:hover) + div.option-container,
    &:has(input:focus) + div.option-container {
        border-color: #010205;
    }
`;

const StyledOption = styled.div`
    display: flex;
    gap: 16px;
    align-items: center;

    padding: 8px 12px 8px 12px;
    cursor: pointer;
    border-radius: 4px;

    &:hover {
        background: #d8d8db;
    }
`;

const StyledOptionContainer = styled.div`
    position: absolute;
    width: 100%;
    z-index: 10;
    background: #fff;
    border: 2px solid #6b6d70;
    border-top: none;
    border-radius: 4px;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    background: white;

    margin-top: -3px;

    div.line {
        padding-top: 3px;
        border: none;
        border-bottom: 1px solid #6b6d70;
    }

    div.content {
        padding: 8px;
        display: flex;
        flex-direction: column;
        gap: 8px;

        overflow-wrap: break-word;

        max-height: 300px;
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;

        scrollbar-width: thin;
        scrollbar-color: auto;

        .no-results {
            padding: 8px 12px 8px 12px;
        }
    }
`;

type NearbyDealerProps = {
    event: JourneyEventDataFragment;
    required: boolean;
    name: string;
    label: string;
    onChange?: (dealerValue: string) => void;
};

const NearbyDealer = ({ event, label, name, required, onChange, ...props }: NearbyDealerProps) => {
    const translatedString = useTranslatedString();
    const { t } = useTranslation(['common', 'eventApplicantForm']);
    const [searchText, setSearchText] = useState<string>('');
    const [isOpen, setIsOpen] = useState<boolean>(false);
    const dropdownTimeoutRef = useRef<NodeJS.Timeout | null>(null);

    const wrapperRef = useRef<HTMLDivElement>(null);
    const inputRef = useRef<HTMLInputElement>(null);

    const company = useCompany(true);

    const [field, meta, { setValue, setTouched }] = useField({ name });

    const filter = useMemo(
        (): NearbyDealerFilteringRule => ({
            companyId: event.module.company.id,
            productionOnly: !event.privateAccess,
            searchText,
        }),
        [event.module.company.id, event.privateAccess, searchText]
    );

    const { data, loading } = useGetNearbyDealersQuery({
        fetchPolicy: 'cache-and-network',
        variables: { filter: { ...filter, companyId: company?.id, searchText } },
    });

    const dealers = useMemo(() => {
        if (loading) {
            return [];
        }

        const dealersFromApi = data?.getNearbyDealers || [];

        if (event.dealerIds.length > 0) {
            const filteredDealers = dealersFromApi.filter(dealer =>
                event.dealers.some(dealerEvent => dealerEvent.id === dealer.id)
            );

            return filteredDealers;
        }

        return [];
    }, [data?.getNearbyDealers, event.dealerIds.length, event.dealers, loading]);

    const handleOpenOrCloseDropdown = useCallback(isOpen => {
        if (dropdownTimeoutRef.current) {
            clearTimeout(dropdownTimeoutRef.current);
        }

        dropdownTimeoutRef.current = setTimeout(() => {
            setIsOpen(isOpen);
        });
    }, []);

    // handle clear button click
    const handleClearButtonClick = useCallback(
        (e: MouseEvent) => {
            e.stopPropagation();
            e.preventDefault();

            inputRef.current.value = '';
            setSearchText('');
            setValue(undefined);

            if (onChange) {
                onChange(undefined);
            }
        },
        [onChange, setValue]
    );

    const attachClearInputEvent = useCallback(() => {
        if (!wrapperRef.current) {
            return;
        }

        const wrapper = wrapperRef.current;

        const textField = wrapper.querySelector('p-text-field-wrapper') as HTMLDivElement;
        const textFieldShadowRoot = textField?.shadowRoot;

        // Add event listener to the clear button inside the text field
        const buttonPureShadowRoot = textFieldShadowRoot?.querySelector(
            '.wrapper > p-button-pure[tabindex="-1"]'
        )?.shadowRoot;

        const button = buttonPureShadowRoot?.querySelector('button');
        if (button) {
            button.removeEventListener('click', handleClearButtonClick);
            // Re-add the event listener to ensure it is set up correctly
            button.addEventListener('click', handleClearButtonClick);
        }
    }, [handleClearButtonClick]);

    const handleSelectOptionChange = useCallback(
        (dealer: NearbyDealersDataFragment) => {
            setValue(dealer.id);
            handleOpenOrCloseDropdown(false);

            inputRef.current.value = translatedString(dealer.legalName);

            attachClearInputEvent();

            if (onChange) {
                onChange(dealer.id);
            }
        },
        [attachClearInputEvent, handleOpenOrCloseDropdown, onChange, setValue, translatedString]
    );

    // dealer options
    const selectOptions = useMemo(
        () =>
            dealers.map((dealer, index) => {
                const distance = dealer.calculatedDistance ? `${Math.round(dealer.calculatedDistance)} km` : '';

                return (
                    // onMouseDown fires before onBlur
                    // so the selection logic will run before the input loses focus and the dropdown closes.
                    <StyledOption key={dealer.id} onMouseDown={() => handleSelectOptionChange(dealer)}>
                        {distance && <PTag color="notification-info-soft">{distance}</PTag>}
                        <div>
                            <PText>{translatedString(dealer.legalName)}</PText>

                            {distance && dealer.contact.address?.defaultValue && (
                                <PText color="contrast-high" size="x-small">
                                    {translatedString(dealer.contact.address)}
                                </PText>
                            )}
                        </div>
                    </StyledOption>
                );
            }),
        [dealers, handleSelectOptionChange, translatedString]
    );

    // handle search button click
    const handleSearchButtonClick = useCallback(
        (e: MouseEvent) => {
            e.stopPropagation();
            e.preventDefault();

            setSearchText(inputRef.current?.value || '');

            handleOpenOrCloseDropdown(true);
        },
        [handleOpenOrCloseDropdown]
    );

    const isDisabled = useMemo(() => dealers.length === 1 && !searchText, [searchText, dealers.length]);

    useEffect(() => {
        if (!wrapperRef.current) {
            return;
        }

        const wrapper = wrapperRef.current;

        componentsReady(wrapper).then(() => {
            // Text field
            const textField = wrapper.querySelector('p-text-field-wrapper') as HTMLDivElement;
            const textFieldShadowRoot = textField?.shadowRoot;

            const textFieldWrapper = textFieldShadowRoot?.querySelector('.wrapper') as HTMLDivElement;
            if (textFieldWrapper) {
                textFieldWrapper.style.background = 'white';
            }

            if (isDisabled) {
                setTimeout(() => {
                    // Found an issue on input field where if the state is changed from "search" > "text" when the isDisabled value changed
                    // The related buttons of it (search & clear) is still remain, hence require to handle the buttons manually
                    const fieldClearButton = textFieldShadowRoot?.querySelector('p-button-pure') as HTMLElement;
                    if (fieldClearButton) {
                        fieldClearButton.style.display = 'none';
                    }
                    const fieldSearchButton = textFieldShadowRoot?.querySelector('p-icon') as HTMLElement;
                    if (fieldSearchButton) {
                        fieldSearchButton.style.display = 'none';
                    }
                }, 200);
            }

            // Add event listener to the search button inside the text field
            const buttonPureShadowRoot = textFieldShadowRoot?.querySelector('.wrapper > p-button-pure')?.shadowRoot;
            const button = buttonPureShadowRoot?.querySelector('button');
            if (button) {
                button.addEventListener('click', handleSearchButtonClick);
            }

            // Cleanup on unmount or dependency change
            return () => {
                if (button) {
                    button.removeEventListener('click', handleSearchButtonClick);
                }
            };
        });
    }, [handleSearchButtonClick, isDisabled]);

    useEffect(() => {
        function handleClickOutside(event: MouseEvent) {
            if (wrapperRef.current && !wrapperRef.current.contains(event.target as Node)) {
                handleOpenOrCloseDropdown(false);
            }
        }

        document.addEventListener('mousedown', handleClickOutside);

        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [handleOpenOrCloseDropdown]);

    // Handle input change to clear search text and close dropdown if input is empty
    const onInputChange = useCallback(
        (e: React.ChangeEvent<HTMLInputElement>) => {
            if (!inputRef.current.value) {
                setSearchText('');
                setValue(undefined);

                if (onChange) {
                    onChange(undefined);
                }
            }
        },
        [onChange, setValue]
    );

    // Handle input click to open dropdown and clear selected dealer
    const onInputClick = useCallback(() => {
        setTouched(false);
        handleOpenOrCloseDropdown(true);
    }, [handleOpenOrCloseDropdown, setTouched]);

    const onInputKeyDown = useCallback((e: React.KeyboardEvent<HTMLInputElement>) => {
        if (e.key === 'Enter') {
            e.preventDefault();
            setSearchText(inputRef.current?.value || '');
        }
    }, []);

    const onInputFocus = useCallback(() => {
        handleOpenOrCloseDropdown(true);
    }, [handleOpenOrCloseDropdown]);

    const onInputBlur = useCallback(() => {
        handleOpenOrCloseDropdown(false);
    }, [handleOpenOrCloseDropdown]);

    const { hasError, errorMessage } = useMemo(
        () => ({
            hasError: !!meta?.error && meta?.touched,
            errorMessage: Array.isArray(meta?.error) ? '' : meta?.error,
        }),
        [meta?.error, meta?.touched]
    );

    const dealer = useMemo(() => dealers.find(dealer => dealer.id === field.value) || null, [dealers, field.value]);

    useEffect(() => {
        if (selectOptions.length === 1 && dealer) {
            inputRef.current.value = translatedString(dealer.legalName);
        }
    }, [selectOptions.length, dealer, translatedString]);

    // If there is no selected dealer and only one dealer available, select it automatically
    useEffect(() => {
        if (!field.value && dealers.length === 1 && !searchText) {
            setValue(dealers[0].id);
        }
    }, [dealers, field.value, searchText, setValue]);

    return (
        <FormItem {...props}>
            <>
                <StyledDiv ref={wrapperRef}>
                    <StyledPTextField label={label} state={hasError ? 'error' : undefined} submitButton={!isDisabled}>
                        <input
                            ref={inputRef}
                            disabled={isDisabled}
                            onBlur={onInputBlur}
                            onChange={onInputChange}
                            onClick={onInputClick}
                            onFocus={onInputFocus}
                            onKeyDown={onInputKeyDown}
                            placeholder={t('eventApplicantForm:fields.findNearbyDealer.placeholder')}
                            required={required}
                            type={isDisabled ? 'text' : 'search'}
                        />
                        {hasError && <span slot="message">{errorMessage}</span>}
                    </StyledPTextField>

                    {!loading && !hasError && isOpen && (
                        <StyledOptionContainer className="option-container">
                            <div className="line" />
                            <div className="content">
                                {selectOptions.length === 0 && (
                                    <div className="no-results">
                                        <PText>{t('common:noResults')}</PText>
                                    </div>
                                )}
                                {selectOptions.length > 0 && selectOptions}
                            </div>
                        </StyledOptionContainer>
                    )}
                </StyledDiv>

                {dealer && <DealerInfo dealer={dealer} />}
            </>
        </FormItem>
    );
};

export default NearbyDealer;
