query getSalesControlBoard($filter: SalesControlBoardFilterPayload!) {
    salesControlBoard: getSalesControlBoard(filter: $filter) {
        fiCommissions {
            salesConsultantName
            inHouseFinanceTarget
            inHouseFinanceMtd
            inHouseFinanceYtd
            inHouseFinance3MAvg
            inHouseInsuranceTarget
            inHouseInsuranceMtd
            inHouseInsuranceYtd
            inHouseInsurance3MAvg
        }

        weekFunnels {
            start
            end
        }

        progressGoal {
            retailTargetMonth
            retailActualMonth
            retailMonthRate
            retailMonthDev
            retailTargetYtd
            retailActualYtd
            retailYtdRate
            retailYtdDev
            financeTarget
            financeActualRate
            insuranceTarget
            insuranceActualRate
        }

        performanceOverview {
            leadsCreated
            testDrives
            salesOffers
            orderIntakes
            retails
        }
    }
}
