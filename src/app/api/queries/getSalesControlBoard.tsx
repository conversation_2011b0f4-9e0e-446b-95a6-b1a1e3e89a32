import type * as SchemaTypes from '../types';

import { gql } from '@apollo/client';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type GetSalesControlBoardQueryVariables = SchemaTypes.Exact<{
  filter: SchemaTypes.SalesControlBoardFilterPayload;
}>;


export type GetSalesControlBoardQuery = (
  { __typename: 'Query' }
  & { salesControlBoard: (
    { __typename: 'SalesControlBoard' }
    & { fiCommissions: Array<(
      { __typename: 'FICommission' }
      & Pick<SchemaTypes.FiCommission, 'salesConsultantName' | 'inHouseFinanceTarget' | 'inHouseFinanceMtd' | 'inHouseFinanceYtd' | 'inHouseFinance3MAvg' | 'inHouseInsuranceTarget' | 'inHouseInsuranceMtd' | 'inHouseInsuranceYtd' | 'inHouseInsurance3MAvg'>
    )>, weekFunnels: Array<(
      { __typename: 'WeekFunnel' }
      & Pick<SchemaTypes.WeekFunnel, 'start' | 'end'>
    )>, progressGoal?: SchemaTypes.Maybe<(
      { __typename: 'ProgressGoal' }
      & Pick<SchemaTypes.ProgressGoal, 'retailTargetMonth' | 'retailActualMonth' | 'retailMonthRate' | 'retailMonthDev' | 'retailTargetYtd' | 'retailActualYtd' | 'retailYtdRate' | 'retailYtdDev' | 'financeTarget' | 'financeActualRate' | 'insuranceTarget' | 'insuranceActualRate'>
    )>, performanceOverview: (
      { __typename: 'SalesPerformanceOverview' }
      & Pick<SchemaTypes.SalesPerformanceOverview, 'leadsCreated' | 'testDrives' | 'salesOffers' | 'orderIntakes' | 'retails'>
    ) }
  ) }
);


export const GetSalesControlBoardDocument = /*#__PURE__*/ gql`
    query getSalesControlBoard($filter: SalesControlBoardFilterPayload!) {
  salesControlBoard: getSalesControlBoard(filter: $filter) {
    fiCommissions {
      salesConsultantName
      inHouseFinanceTarget
      inHouseFinanceMtd
      inHouseFinanceYtd
      inHouseFinance3MAvg
      inHouseInsuranceTarget
      inHouseInsuranceMtd
      inHouseInsuranceYtd
      inHouseInsurance3MAvg
    }
    weekFunnels {
      start
      end
    }
    progressGoal {
      retailTargetMonth
      retailActualMonth
      retailMonthRate
      retailMonthDev
      retailTargetYtd
      retailActualYtd
      retailYtdRate
      retailYtdDev
      financeTarget
      financeActualRate
      insuranceTarget
      insuranceActualRate
    }
    performanceOverview {
      leadsCreated
      testDrives
      salesOffers
      orderIntakes
      retails
    }
  }
}
    `;

/**
 * __useGetSalesControlBoardQuery__
 *
 * To run a query within a React component, call `useGetSalesControlBoardQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetSalesControlBoardQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetSalesControlBoardQuery({
 *   variables: {
 *      filter: // value for 'filter'
 *   },
 * });
 */
export function useGetSalesControlBoardQuery(baseOptions: Apollo.QueryHookOptions<GetSalesControlBoardQuery, GetSalesControlBoardQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetSalesControlBoardQuery, GetSalesControlBoardQueryVariables>(GetSalesControlBoardDocument, options);
      }
export function useGetSalesControlBoardLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetSalesControlBoardQuery, GetSalesControlBoardQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetSalesControlBoardQuery, GetSalesControlBoardQueryVariables>(GetSalesControlBoardDocument, options);
        }
export type GetSalesControlBoardQueryHookResult = ReturnType<typeof useGetSalesControlBoardQuery>;
export type GetSalesControlBoardLazyQueryHookResult = ReturnType<typeof useGetSalesControlBoardLazyQuery>;
export type GetSalesControlBoardQueryResult = Apollo.QueryResult<GetSalesControlBoardQuery, GetSalesControlBoardQueryVariables>;