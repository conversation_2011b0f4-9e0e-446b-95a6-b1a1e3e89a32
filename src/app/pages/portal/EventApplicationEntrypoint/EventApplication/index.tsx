/* eslint-disable max-len */
import dayjs from 'dayjs';
import { isEmpty } from 'lodash/fp';
import { Dispatch, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import type {
    EventApplicationEntrypointContextDataFragment,
    EventApplicationModuleDebugJourneyFragment,
} from '../../../../api/fragments';
import { type EventCustomizedFieldInput } from '../../../../api/types';
import { useUploadOcrFiles } from '../../../../components/ocr';
import OcrFilesManager from '../../../../components/ocr/OcrFilesManager';
import { useThemeComponents } from '../../../../themes/hooks';
import { getInitialValues } from '../../../../utilities/kycPresets';
import useHandleError from '../../../../utilities/useHandleError';
import useAgreementSubmission from '../../../shared/CIPage/ConsentAndDeclarations/useAgreementSubmission';
import { getAgreementValues } from '../../../shared/CIPage/ConsentAndDeclarations/useAgreementsValues';
import useDeleteApplicationDocument from '../../../shared/useDeleteApplicationDocument';
import useUploadApplicationDocument, { UploadDocumentKind } from '../../../shared/useUploadApplicationDocument';
import useAppointmentSubmission from '../../StandardApplicationEntrypoint/AppointmentPage/useAppointmentSubmission';
import useVisitAppointmentSubmission from '../../StandardApplicationEntrypoint/AppointmentPage/useVisitAppointmentSubmission';
import useCustomerDetailsSubmission from '../../StandardApplicationEntrypoint/CustomerDetailsPage/useCustomerDetailsSubmission';
import useUpdateApplicationFields from '../../StandardApplicationEntrypoint/CustomerDetailsPage/useUpdateApplicationFields';
import type { Action, State } from '../../StandardApplicationEntrypoint/Journey/shared';
import { getApplicantKyc } from '../../StandardApplicationEntrypoint/KYCPage/getKyc';
import { retrieveAppointmentModule, retrieveAppointmentScenarios } from '../ApplicantForm/AppointmentDetailsSection';
import ApplicantForm from '../ApplicantForm/NewApplicantForm';
import { ApplicantFormValues } from '../ApplicantForm/shared';
import useUpdateEventApplication from '../ApplicantForm/useUpdateEventApplication';
import {
    EventJourneyKycAndAgreementProvider,
    useEventJourneyKycAndAgreementContext,
} from '../Entrypoint/EventJourneyKycAndAgreement';
import { EventJourneySetupProvider, useEventJourneySetupContext } from '../Entrypoint/EventJourneySetup';
import type { EventApplicationState } from '../Journey/shared';
import { usePersistEventJourneyValues } from '../Journey/usePersistEventJourneyValues';

export type EventApplicationProps = {
    endpoint: EventApplicationEntrypointContextDataFragment;
    state: State<EventApplicationState>;
    dispatch: Dispatch<Action<EventApplicationState>>;
};

/**
 * This page use mostly the same logic with the Entrypoint
 * But, it used for redirection from MyInfo, which is the KYC page, which is the initial page like Entrypoint
 */
const Inner = ({ state, dispatch }: Omit<EventApplicationProps, 'endpoint'>) => {
    const { t } = useTranslation(['eventApplicantForm', 'common']);
    const { notification } = useThemeComponents();

    const { token, application } = state;
    const {
        applicant,
        applicantAgreements,
        applicantKYC: applicationApplicantKYC,
        appointmentStage,
        configuration: applicationConfiguration,
        customizedFields,
        dealerId,
        event,
        lead: { capValues },
        remarks,
        tradeInVehicle,
        vehicle,
        visitAppointmentStage,
    } = application;

    const { endpoint } = useEventJourneySetupContext();
    const { customerKind, prefill } = useEventJourneyKycAndAgreementContext();
    const { save: persistEventJourneyValue, persistedValue: persistedEventValue } = usePersistEventJourneyValues();

    const uploadDocument = useUploadApplicationDocument(token, UploadDocumentKind.ApplicationAndLead);
    const removeDocument = useDeleteApplicationDocument(UploadDocumentKind.ApplicationAndLead, token);

    const updateEvent = useUpdateEventApplication(endpoint);
    const submitAgreements = useAgreementSubmission();
    const submitCustomerDetails = useCustomerDetailsSubmission();
    const updateApplicationFields = useUpdateApplicationFields();

    const uploadOcrFiles = useUploadOcrFiles();

    /**
     * existing `submitAppointment` is test drive appointment
     * ::
     * submitVisitAppointment is only serve for `Showroom Visit`
     */
    const submitAppointment = useAppointmentSubmission();
    const submitVisitAppointment = useVisitAppointmentSubmission();

    const onSubmit = useHandleError(
        async ({ appointment, visitAppointment, ...values }: ApplicantFormValues) => {
            notification.loading({
                content: t('eventApplicantForm:messages.creationSubmitting'),
                duration: 0,
                key: 'primary',
            });

            const updateEventResult = await updateEvent(token, values, customerKind);
            const submitAgreementResult = await submitAgreements(
                updateEventResult.token,
                values.agreements,
                customerKind,
                values.hasGuarantor
            );

            const showRemarks = application?.bank?.showCommentsField;

            if (showRemarks) {
                // we wait to update remarks first
                await updateApplicationFields(submitAgreementResult.token, values.remarks);
                // then we later call the KYC
                // as calling submit customer will immediately call the next step and will have not the remarks
            }

            const submitCustomerDetailsResult = await submitCustomerDetails({
                token: submitAgreementResult.token,
                fields: values.customer.fields,
                customerKind,
                sameCorrespondenceAddress: prefill,
                customerCiamId: values.customerCiamId,
                isCustomerSearchPerformed: values.isCustomerSearchPerformed,
            });
            await uploadOcrFiles(submitCustomerDetailsResult.token);

            const { appointmentModule, visitAppointmentModule } = retrieveAppointmentModule(
                application?.event?.module.__typename === 'EventApplicationModule' &&
                    (application?.event?.module as EventApplicationModuleDebugJourneyFragment)
            );

            const { hasShowroomVisit, hasTestDrive } = retrieveAppointmentScenarios(
                application?.event?.module.__typename === 'EventApplicationModule' &&
                    (application?.event?.module as EventApplicationModuleDebugJourneyFragment),
                application?.event
            );

            let temporarilyJourneyResult = submitCustomerDetailsResult;

            // submit for appointment test drive
            if (hasTestDrive && values.configuration.testDrive) {
                const result = await submitAppointment(
                    temporarilyJourneyResult.token,
                    appointment,
                    appointmentModule,
                    application?.event?.displayAppointmentDatepicker,
                    application?.event?.module.company.timeZone,
                    event
                );
                temporarilyJourneyResult = result;
            }

            // submit for appointment showroom visit
            if (hasShowroomVisit && values.configuration.visitAppointment) {
                const result = await submitVisitAppointment(
                    temporarilyJourneyResult.token,
                    visitAppointment,
                    visitAppointmentModule,
                    true,
                    application?.event?.module.company.timeZone
                );
                temporarilyJourneyResult = result;
            }

            if (temporarilyJourneyResult.__typename !== 'ApplicationJourney') {
                throw new Error('unsupported journey context');
            }

            notification.destroy('primary');

            if (temporarilyJourneyResult.application.__typename !== 'EventApplication') {
                throw new Error('unexpected type');
            }

            persistEventJourneyValue({
                ...persistedEventValue,
                appointment,
                visitAppointment,
                ...values,
            });

            dispatch({
                type: 'next',
                token: temporarilyJourneyResult.token,
                application: temporarilyJourneyResult.application,
            });
        },
        [
            notification,
            t,
            updateEvent,
            token,
            customerKind,
            submitAgreements,
            application?.bank?.showCommentsField,
            application?.event,
            submitCustomerDetails,
            prefill,
            uploadOcrFiles,
            persistEventJourneyValue,
            persistedEventValue,
            dispatch,
            updateApplicationFields,
            submitAppointment,
            event,
            submitVisitAppointment,
        ]
    );

    const { appointment, visitAppointment } = useMemo(() => {
        const {
            module: { company },
        } = event;

        const temporaryAppointment = persistedEventValue?.appointment;
        const temporaryVisitAppointment = persistedEventValue?.visitAppointment;

        const getAppointmentDate = (
            useCurrentDateTime: boolean,
            slot: string | Date,
            temporaryValue: string | Date | dayjs.Dayjs
        ) => {
            if (useCurrentDateTime) {
                return temporaryValue;
            }

            return slot ? dayjs(slot).tz(company.timeZone) : temporaryAppointment.date;
        };

        const getAppointmentTime = (useCurrentDateTime: boolean, slot: string | Date, temporaryValue: string) => {
            if (useCurrentDateTime) {
                return temporaryValue;
            }

            return slot ? dayjs(slot).tz(company.timeZone).format('HH:mm') : temporaryValue;
        };

        const appointmentDetails = appointmentStage
            ? {
                  date: getAppointmentDate(
                      temporaryAppointment?.useCurrentDateTime,
                      appointmentStage.bookingTimeSlot?.slot,
                      temporaryAppointment.date
                  ),
                  time: getAppointmentTime(
                      temporaryAppointment?.useCurrentDateTime,
                      appointmentStage.bookingTimeSlot?.slot,
                      temporaryAppointment.time
                  ),
                  useCurrentDateTime: temporaryAppointment?.useCurrentDateTime ?? false,
              }
            : temporaryAppointment;

        const visitAppointmentDetails = visitAppointmentStage
            ? {
                  date: getAppointmentDate(
                      temporaryVisitAppointment?.useCurrentDateTime,
                      visitAppointmentStage.bookingTimeSlot?.slot,
                      temporaryVisitAppointment.date
                  ),
                  time: getAppointmentTime(
                      temporaryVisitAppointment?.useCurrentDateTime,
                      visitAppointmentStage.bookingTimeSlot?.slot,
                      temporaryVisitAppointment.time
                  ),
                  useCurrentDateTime: temporaryVisitAppointment?.useCurrentDateTime ?? false,
              }
            : temporaryVisitAppointment;

        return { appointment: appointmentDetails, visitAppointment: visitAppointmentDetails };
    }, [appointmentStage, event, persistedEventValue, visitAppointmentStage]);

    const initialValues: ApplicantFormValues = useMemo(() => {
        const getVehicleValue = () => {
            if (!vehicle) {
                return null;
            }

            if (vehicle.__typename !== 'LocalVariant') {
                throw new Error('Invalid Variant');
            }

            return vehicle;
        };

        const vehicleValue = getVehicleValue();
        const vehicleInterest = vehicleValue
            ? {
                  model: vehicleValue?.model.parentModel ? vehicleValue.model.parentModelId : vehicleValue?.model.id,
                  subModel: vehicleValue?.submodelId,
                  variant: vehicleValue?.id,
              }
            : persistedEventValue?.vehicleInterest;

        const applicantKYC = getApplicantKyc(applicationApplicantKYC);
        const initialCustomerFields = applicant ? applicant.fields : [];
        const initialCustomerValues = { fields: getInitialValues(initialCustomerFields, applicantKYC) };
        const persistentCustomerValue = persistedEventValue?.customer ?? initialCustomerValues;

        const customer = initialCustomerFields.length ? initialCustomerValues : persistentCustomerValue;

        const initialRemarks = remarks ?? persistedEventValue?.remarks;

        return {
            customer,
            agreements: !isEmpty(getAgreementValues(applicantAgreements))
                ? getAgreementValues(applicantAgreements)
                : persistedEventValue?.agreements,
            configuration: applicationConfiguration ?? persistedEventValue?.configuration,
            vehicleInterest,
            isCorporateCustomer: false,
            tradeInVehicle,
            hasGuarantor: false,
            dealerId,
            prefill: false,
            customizedFields: customizedFields as EventCustomizedFieldInput[],
            appointment,
            visitAppointment,

            remarks: initialRemarks ?? '',
            capValues: capValues ?? persistedEventValue?.capValues,
            customerCiamId:
                applicant.__typename === 'LocalCustomer'
                    ? applicant.customerCiamId
                    : persistedEventValue?.customerCiamId,
        };
    }, [
        persistedEventValue?.vehicleInterest,
        persistedEventValue?.customer,
        persistedEventValue?.remarks,
        persistedEventValue?.agreements,
        persistedEventValue?.configuration,
        persistedEventValue?.capValues,
        persistedEventValue?.customerCiamId,
        applicationApplicantKYC,
        applicant,
        remarks,
        applicantAgreements,
        applicationConfiguration,
        tradeInVehicle,
        dealerId,
        customizedFields,
        appointment,
        visitAppointment,
        capValues,
        vehicle,
    ]);

    return (
        <ApplicantForm
            dispatch={dispatch}
            initialValues={initialValues}
            onSubmit={onSubmit}
            removeDocument={removeDocument}
            state={state}
            uploadDocument={uploadDocument}
        />
    );
};

const EventApplication = ({ endpoint, state, dispatch }: EventApplicationProps) => (
    <OcrFilesManager>
        <EventJourneySetupProvider application={state.application} endpoint={endpoint} event={state.application.event}>
            <EventJourneyKycAndAgreementProvider>
                <Inner dispatch={dispatch} state={state} />
            </EventJourneyKycAndAgreementProvider>
        </EventJourneySetupProvider>
    </OcrFilesManager>
);

export default EventApplication;
