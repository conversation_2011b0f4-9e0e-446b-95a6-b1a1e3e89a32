import { useApolloClient } from '@apollo/client';
import { useEffect, useMemo, useState } from 'react';
import { Navigate, useLocation } from 'react-router';
import { EventApplicationEntrypointContextDataFragment } from '../../../../api/fragments';
import {
    GetApplicationJourneyDocument,
    GetApplicationJourneyQuery,
    GetApplicationJourneyQueryVariables,
} from '../../../../api/queries';
import { getPorscheIDConfigByApplicationJourney } from '../../../../components/PorscheID/getPorscheIDConfig';
import PortalLoadingElement from '../../../../components/PortalLoadingElement';
import { useAccount } from '../../../../components/contexts/AccountContextManager';
import { useRouter } from '../../../../components/contexts/shared';
import NotFoundResult from '../../../../components/results/NotFoundResult';
import { useThemeComponents } from '../../../../themes/hooks';
import getApolloErrors from '../../../../utilities/getApolloErrors';
import { useParseJWTPayload } from '../../../../utilities/parseJWTPayload';
import { getApplicationJourneyStages } from '../../../shared/JourneyPage/mapJourneySteps';
import { JourneyStage } from '../../StandardApplicationEntrypoint/Journey/shared';
import JourneyController from './JourneyController';

export type JourneyBootstrapProps = {
    endpoint: EventApplicationEntrypointContextDataFragment;
    initialToken: string;
    initialStage: JourneyStage;
};

type TokenPayload = {
    applicationModuleId: { $oid: string };
    origin: 'draft' | 'remote-applicant' | 'remote-guarantor';
};

const JourneyBootstrap = ({ initialToken, initialStage, endpoint }: JourneyBootstrapProps) => {
    const apolloClient = useApolloClient();

    const { applicationModuleId } = useParseJWTPayload<TokenPayload>(initialToken);
    const [state, setInitialState] = useState<GetApplicationJourneyQuery['result'] | null>(null);
    const [error, setError] = useState<Error | null>(null);

    const { notification } = useThemeComponents();
    const { layout } = useRouter();
    const isPorscheV3Layout = layout?.__typename === 'PorscheV3Layout';

    const isInitialized = !!state;
    const isValid = applicationModuleId.$oid === endpoint.eventApplicationModule.id;

    useEffect(() => {
        if (isInitialized) {
            // already initialized, we skip it
            return;
        }

        apolloClient
            .query<GetApplicationJourneyQuery, GetApplicationJourneyQueryVariables>({
                query: GetApplicationJourneyDocument,
                fetchPolicy: 'no-cache',
                variables: { token: initialToken, refreshToken: true },
            })
            .then(result => setInitialState(result.data.result))
            .catch(error => {
                const apolloErrors = getApolloErrors(error);

                if (apolloErrors !== null) {
                    const { $root: rootError } = apolloErrors;

                    if (rootError) {
                        notification.error(rootError);
                    }
                } else {
                    console.error(error);
                }

                setError(error);
            });
    }, [isInitialized, isValid, apolloClient, initialToken, notification]);

    const eventApplication = useMemo(
        () => (state?.application.__typename === 'EventApplication' ? state?.application : null),
        [state?.application]
    );

    const { isPorscheIdLoginMandatory } = eventApplication
        ? getPorscheIDConfigByApplicationJourney(eventApplication)
        : { isPorscheIdLoginMandatory: false };

    const stages = useMemo(
        () => [
            JourneyStage.RequiredDetails,
            ...(isPorscheIdLoginMandatory ? [JourneyStage.PorscheIdLoginRegister] : []),
            ...(eventApplication ? getApplicationJourneyStages(eventApplication) : []),
        ],
        [eventApplication, isPorscheIdLoginMandatory]
    );

    const user = useAccount(true);
    const location = useLocation();

    if (!state) {
        return <PortalLoadingElement />;
    }

    if (!isValid || !!error || !eventApplication) {
        // things are not as expected
        return <NotFoundResult />;
    }

    if (eventApplication.event.privateAccess && !user) {
        return <Navigate state={{ nextPage: location.pathname }} to={{ pathname: '/auth/signIn' }} />;
    }

    return (
        <JourneyController
            endpoint={endpoint}
            initialApplication={eventApplication}
            initialStage={initialStage}
            initialToken={state.token}
            journeyStages={stages}
        />
    );
};

export default JourneyBootstrap;
