/* eslint-disable max-len */
import { orderBy } from 'lodash/fp';
import { createContext, useCallback, useContext, useEffect, useMemo, useState } from 'react';
import urljoin from 'url-join';
import { EventApplicationEntrypointContextDataFragment } from '../../../../api/fragments/EventApplicationEntrypointContextData';
import { JourneyEventDataFragment } from '../../../../api/fragments/JourneyEventData';
import { VehicleCalculatorSpecsFragment } from '../../../../api/fragments/VehicleCalculatorSpecs';
import { useListLocalVariantsForCalculatorQuery } from '../../../../api/queries/listLocalVariantsForCalculator';
import { ApplicationScenario, EventCustomizedFieldInput, Purpose } from '../../../../api/types';
import { getPorscheIDConfigByEvent } from '../../../../components/PorscheID/getPorscheIDConfig';
import PortalLoadingElement from '../../../../components/PortalLoadingElement';
import { useAccount } from '../../../../components/contexts/AccountContextManager';
import NotFoundResult from '../../../../components/results/NotFoundResult';
import { useHeaderContext } from '../../../../layouts/HeaderContextManager';
import useActiveState from '../../../../utilities/useActiveStage';
import { getDealershipPaymentSetting } from '../../../admin/ModuleDetailsPage/modules/implementations/shared/getDealershipPaymentSetting';
import { JourneyStage } from '../../StandardApplicationEntrypoint/Journey/shared';
import type { EventApplicationState } from '../Journey/shared';
import TimeoutPage from '../TimeoutPage';

/* 
    Since VF-1406: This journey setup provider is not being used anymore, since on new layout it's not using this setup provider
    but let's keep it for now in case of revert needed
*/

type EventJourneySetupContextProps = {
    event: JourneyEventDataFragment;
    endpoint: EventApplicationEntrypointContextDataFragment;
    variants: Array<Extract<VehicleCalculatorSpecsFragment, { __typename: 'LocalVariant' }>> | null;
    eventModule: Extract<JourneyEventDataFragment['module'], { __typename: 'EventApplicationModule' }>;
    customizedFieldsInput: EventCustomizedFieldInput[];
    showLiveChat: boolean;
    showResetKYCButton: boolean;
    liveChatSetting?: Extract<
        JourneyEventDataFragment['module'],
        { __typename: 'EventApplicationModule' }
    >['liveChatSetting'];
    getDraftJourneyStages: (selectedDealerId?: string | null) => JourneyStage[];
    isSubModelRequired: boolean;
    setIsSubModelRequired: React.Dispatch<React.SetStateAction<boolean>>;
};

const EventJourneySetupContext = createContext<EventJourneySetupContextProps>(null);

type EventJourneySetupProviderProps = {
    endpoint: EventApplicationEntrypointContextDataFragment;
    event: JourneyEventDataFragment;
    children?: React.ReactNode;
    application?: EventApplicationState;
};

export const EventJourneySetupProvider = ({
    event,
    children,
    endpoint,
    application,
}: EventJourneySetupProviderProps) => {
    const [isSubModelRequired, setIsSubModelRequired] = useState(false);

    // Fetch variants data
    const { data: variantData, loading: loadingVariant } = useListLocalVariantsForCalculatorQuery({
        fetchPolicy: 'cache-and-network',
        variables: {
            filter: {
                purpose: Purpose.Production,
                moduleId: endpoint.eventApplicationModule.vehicleModuleId,
                applicationModuleId: endpoint.eventApplicationModule.id,
                dealerIds: event.dealerIds,
            },
        },
    });

    // Get current user
    const user = useAccount(true);

    // Detect idle state
    const { isActive, reset } = useActiveState();

    const variants = useMemo(() => {
        if (!variantData) {
            return null;
        }

        return variantData.list.items.filter(item => item.__typename === 'LocalVariant');
    }, [variantData]);

    const { isPorscheIdLoginMandatory } = useMemo(() => getPorscheIDConfigByEvent(event), [event]);

    // obtained from `scenarios` as application is not initialized
    const getDraftJourneyStages = useCallback(
        (selectedDealerId?: string | null) => {
            const { scenarios = [], paymentSetting } = event;

            let hasPaymentStage = false;

            if (!selectedDealerId) {
                const hasPaymentScenario = scenarios?.includes(ApplicationScenario.Payment);
                hasPaymentStage = !!paymentSetting && hasPaymentScenario;
            } else {
                hasPaymentStage = !!getDealershipPaymentSetting(selectedDealerId, paymentSetting);
            }

            return [
                JourneyStage.RequiredDetails,
                ...(isPorscheIdLoginMandatory ? [JourneyStage.PorscheIdLoginRegister] : []),
                JourneyStage.ApplicantKYC,
                ...(hasPaymentStage ? [JourneyStage.Deposit] : []),
            ];
        },
        [event, isPorscheIdLoginMandatory]
    );

    // Memoize necessary values
    const values = useMemo((): EventJourneySetupContextProps => {
        const eventCustomizedFieldsInput = event.customizedFields
            ? orderBy('order', 'asc', event.customizedFields).map(({ id, order, __typename, ...rest }) => ({
                  ...rest,
                  value: '',
              }))
            : [];

        return {
            event,
            endpoint,
            variants,
            getDraftJourneyStages,
            eventModule: event.module.__typename === 'EventApplicationModule' ? event.module : null,
            customizedFieldsInput:
                (application?.customizedFields as EventCustomizedFieldInput[]) ?? eventCustomizedFieldsInput,
            showLiveChat: event?.showLiveChat && endpoint.eventApplicationModule?.liveChatSetting && !user,
            liveChatSetting:
                event?.module.__typename === 'EventApplicationModule' ? event.module.liveChatSetting : null,
            showResetKYCButton:
                event?.module.__typename === 'EventApplicationModule' ? event.module.showResetKYCButton : false,
            isSubModelRequired,
            setIsSubModelRequired,
        };
    }, [event, endpoint, variants, getDraftJourneyStages, application?.customizedFields, user, isSubModelRequired]);

    const { setLink } = useHeaderContext();

    useEffect(() => {
        // if public access, we make LGF the home page
        if (!event.privateAccess) {
            setLink(urljoin(endpoint.pathname, event.urlSlug));
        }
    }, [endpoint.pathname, event, setLink]);

    // Read loading variants
    if (loadingVariant) {
        return <PortalLoadingElement />;
    }

    if (!isActive && !user) {
        return <TimeoutPage onBack={reset} />;
    }

    // Check module is the same
    if (endpoint.eventApplicationModule.id !== event.module.id) {
        return <NotFoundResult />;
    }

    return <EventJourneySetupContext.Provider value={values}>{children}</EventJourneySetupContext.Provider>;
};

export const useEventJourneySetupContext = () => {
    const context = useContext(EventJourneySetupContext);

    if (!context) {
        throw new Error('EventJourneySetupContext is missing');
    }

    return context;
};
