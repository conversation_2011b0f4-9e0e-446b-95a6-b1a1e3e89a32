/* eslint-disable max-len */
import dayjs from 'dayjs';
import { isNil } from 'lodash/fp';
import { useMemo } from 'react';
import { Navigate, useLocation, useParams, useSearchParams } from 'react-router-dom';
import * as permissionKind from '../../../../../shared/permissions';
import { EventApplicationEntrypointContextDataFragment } from '../../../../api/fragments';
import { useGetJourneyEventQuery } from '../../../../api/queries/getJourneyEvent';
import PortalLoadingElement from '../../../../components/PortalLoadingElement';
import { useAccount } from '../../../../components/contexts/AccountContextManager';
import { useCompanyContext } from '../../../../components/contexts/CompanyContextManager';
import PorscheNotFound from '../../../../components/results/PorscheNotFound';
import hasPermissions from '../../../../utilities/hasPermissions';
import useTranslatedString from '../../../../utilities/useTranslatedString';
import EventEntrypointPage from './EventEntrypointPage';

export type EventEntrypointProps = {
    endpoint: EventApplicationEntrypointContextDataFragment;
};

const EventEntrypoint = ({ endpoint }: EventEntrypointProps) => {
    const { eventSlug } = useParams<{ eventSlug: string }>();

    const [searchParams] = useSearchParams();
    const userParam = searchParams.get('user');

    const { id } = endpoint.eventApplicationModule;
    // first load the event
    const { data: eventData, loading: loadingEvent } = useGetJourneyEventQuery({
        fetchPolicy: 'network-only',
        variables: {
            urlSlug: eventSlug,
            productionOnly: true,
            eventModuleId: id,
        },
    });

    const translate = useTranslatedString();

    const user = useAccount(true);
    const { availableCompanies } = useCompanyContext();
    const location = useLocation();

    const hasValidUserSearchParam = useMemo(() => {
        if (!userParam || !!eventData?.event?.privateAccess) {
            return true;
        }

        return !!eventData?.event?.userIds?.includes(userParam);
    }, [eventData?.event?.privateAccess, eventData?.event?.userIds, userParam]);

    if (loadingEvent) {
        return <PortalLoadingElement />;
    }

    // Event data not found, just throw 404
    if (!eventData || !eventData.event || !hasValidUserSearchParam) {
        return <PorscheNotFound />;
    }

    const eventName = translate(eventData.event.name);
    const { privateAccess } = eventData.event;
    const companyId = eventData.event.module.company.id;

    if (dayjs().isBefore(eventData.event.period.start) || dayjs().isAfter(eventData.event.period.end)) {
        return <PorscheNotFound />;
    }

    // check if event is private
    if (privateAccess) {
        // check if user is logged in
        if (user) {
            // check if user has correct permissions
            const companyMatch = !isNil(
                availableCompanies?.find(({ id: eventCompanyId }) => eventCompanyId === companyId)
            );
            if (companyMatch) {
                // if event is private, active and user has logged and has the correct permissions, check if the event is expired
                if (hasPermissions(eventData.event.permissions, [permissionKind.createApplication])) {
                    return <EventEntrypointPage endpoint={endpoint} event={eventData.event} eventName={eventName} />;
                }

                return <PorscheNotFound />;
            }

            return <PorscheNotFound />;
        }

        return <Navigate state={{ nextPage: location.pathname }} to={{ pathname: '/auth/signIn' }} />;
    }

    return <EventEntrypointPage endpoint={endpoint} event={eventData.event} eventName={eventName} />;
};

export default EventEntrypoint;
