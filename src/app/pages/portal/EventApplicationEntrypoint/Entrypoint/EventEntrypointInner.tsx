/* eslint-disable max-len */
import { FormikHelpers } from 'formik';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { EventApplicationModuleDebugJourneyFragment } from '../../../../api';
import { useRetrieveBlockedAppointmentTimeSlotQuery } from '../../../../api/queries/retrieveBlockedAppointmentTimeSlot';
import { AssetCondition, LocalCustomerFieldSource } from '../../../../api/types';
import PortalLoadingElement from '../../../../components/PortalLoadingElement';
import { useUploadOcrFiles } from '../../../../components/ocr';
import { useThemeComponents } from '../../../../themes/hooks';
import useHandleError from '../../../../utilities/useHandleError';
import {
    hasAppointmentScenario,
    hasVisitAppointmentScenario,
} from '../../../admin/ModuleDetailsPage/modules/implementations/shared';
import useAgreementSubmission from '../../../shared/CIPage/ConsentAndDeclarations/useAgreementSubmission';
import refineCustomerValues from '../../../shared/refineCustomerValues';
import useUploadApplicationDocuments from '../../../shared/useUploadApplicationDocuments';
import useUploadLeadDocuments from '../../../shared/useUploadLeadDocuments';
import {
    MinimumAppointmentModuleSpecs,
    useFirstAvailableSlot,
} from '../../StandardApplicationEntrypoint/AppointmentPage/useAppointmentAvailability';
import useAppointmentSubmission from '../../StandardApplicationEntrypoint/AppointmentPage/useAppointmentSubmission';
import useVisitAppointmentSubmission from '../../StandardApplicationEntrypoint/AppointmentPage/useVisitAppointmentSubmission';
import useCustomerDetailsSubmission from '../../StandardApplicationEntrypoint/CustomerDetailsPage/useCustomerDetailsSubmission';
import useUpdateApplicationFields from '../../StandardApplicationEntrypoint/CustomerDetailsPage/useUpdateApplicationFields';
import { retrieveAppointmentModule, retrieveAppointmentScenarios } from '../ApplicantForm/AppointmentDetailsSection';
import ApplicantForm from '../ApplicantForm/NewApplicantForm';
import { ApplicantFormValues } from '../ApplicantForm/shared';
import useSubmitDraft from '../useSubmitDraft';
import { useEventJourneyKycAndAgreementContext } from './EventJourneyKycAndAgreement';
import { useEventJourneySetupContext } from './EventJourneySetup';

type InnerProps = {
    onSubmit: (values: ApplicantFormValues, helpers: FormikHelpers<ApplicantFormValues>) => void;
    bookedAppointmentTimeSlots: (string | Date)[] | null | undefined;
    appointmentModule: MinimumAppointmentModuleSpecs | null | undefined;
    bookedVisitAppointmentTimeSlots: (string | Date)[] | null | undefined;
    visitAppointmentModule: MinimumAppointmentModuleSpecs | null | undefined;
};

const Inner = ({
    bookedAppointmentTimeSlots,
    bookedVisitAppointmentTimeSlots,
    appointmentModule,
    onSubmit,
    visitAppointmentModule,
}: InnerProps) => {
    const { event, eventModule, customizedFieldsInput } = useEventJourneySetupContext();

    const datePrefill = useFirstAvailableSlot(
        appointmentModule,
        bookedAppointmentTimeSlots,
        eventModule?.company?.timeZone,
        null,
        event
    );

    const visitDatePrefill = useFirstAvailableSlot(
        visitAppointmentModule,
        bookedVisitAppointmentTimeSlots,
        eventModule?.company?.timeZone
    );

    const dealerId = useMemo(() => {
        // if there is only one option available, set it as default
        if (event.dealerIds?.length === 1) {
            return event.dealerIds[0];
        }

        return null;
    }, [event.dealerIds]);

    const initialValues = useMemo(
        (): ApplicantFormValues => ({
            agreements: {},
            customer: { fields: {} },
            configuration: {
                testDrive: (hasAppointmentScenario(event.scenarios) && event.scenarios.length === 1) || false,
                tradeIn: false,
                assetCondition: AssetCondition.New,
                visitAppointment:
                    (hasVisitAppointmentScenario(event.scenarios) && event.scenarios.length === 1) || false,
            },
            vehicleInterest: {
                model: null,
                subModel: null,
                variant: null,
            },
            isCorporateCustomer: false,
            hasGuarantor: false,
            tradeInVehicle: [
                {
                    isSelected: true,
                    make: null,
                    model: null,
                    registrationNumber: null,
                    source: LocalCustomerFieldSource.UserInput,
                },
            ],
            dealerId,
            prefill: false,
            customizedFields: customizedFieldsInput,
            appointment: {
                date: datePrefill?.date || null,
                time: datePrefill?.firstSlot?.value || null,
                useCurrentDateTime: false,
            },
            visitAppointment: {
                date: visitDatePrefill?.date || null,
                time: visitDatePrefill?.firstSlot?.value || null,
                useCurrentDateTime: false,
            },
            remarks: '',
        }),
        [
            customizedFieldsInput,
            datePrefill?.date,
            datePrefill?.firstSlot?.value,
            dealerId,
            event.scenarios,
            visitDatePrefill?.date,
            visitDatePrefill?.firstSlot?.value,
        ]
    );

    return <ApplicantForm initialValues={initialValues} onSubmit={onSubmit} />;
};

const EventEntrypointInner = () => {
    const { t } = useTranslation(['eventApplicantForm']);
    const { notification } = useThemeComponents();

    const navigate = useNavigate();

    const { event, endpoint, eventModule } = useEventJourneySetupContext();
    const { customerKind, prefill } = useEventJourneyKycAndAgreementContext();

    const submitDraft = useSubmitDraft(event.id, endpoint, {
        userIds: event.userIds,
        isPrivateAccess: event.privateAccess,
    });
    const submitAgreements = useAgreementSubmission();
    const submitCustomerDetails = useCustomerDetailsSubmission();
    const updateApplicationFields = useUpdateApplicationFields();
    const uploadOcrFiles = useUploadOcrFiles();
    /**
     * existing `submitAppointment` is test drive appointment
     * ::
     * submitVisitAppointment is only serve for `Showroom Visit`
     */
    const submitAppointment = useAppointmentSubmission();
    const submitVisitAppointment = useVisitAppointmentSubmission();

    // Can't use src/app/pages/shared/useUploadApplicationDocument.ts here
    // Because journey was not initialized yet
    // So use src/app/pages/shared/useUploadApplicationDocuments.ts instead
    const uploadApplicationDocuments = useUploadApplicationDocuments();
    const uploadLeadDocuments = useUploadLeadDocuments();

    const onSubmit = useHandleError(
        async ({ appointment, visitAppointment, ...values }: ApplicantFormValues) => {
            notification.loading({
                content: t('eventApplicantForm:messages.creationSubmitting'),
                duration: 0,
                key: 'primary',
            });

            const submitDraftApplicationResult = await submitDraft(values, customerKind);

            const { application } =
                submitDraftApplicationResult.__typename === 'ApplicationJourney' && submitDraftApplicationResult;
            if (application.__typename !== 'EventApplication') {
                throw new Error('ApplicationKind not found');
            }

            let temporarilyJourneyResult = submitDraftApplicationResult;
            const submitAgreementResult = await submitAgreements(
                temporarilyJourneyResult.token,
                values.agreements,
                customerKind,
                values.hasGuarantor
            );

            const { applicantFields, customerAssetArray } = refineCustomerValues(values.customer.fields);

            const showCommentsToBank =
                submitDraftApplicationResult?.application?.__typename === 'EventApplication' &&
                submitDraftApplicationResult.application.bank?.showCommentsField;

            if (showCommentsToBank) {
                // we wait to update remarks first
                await updateApplicationFields(submitAgreementResult.token, values.remarks);
                // then we later call the KYC
                // as calling submit customer will immediately call the next step and will have not the remarks
            }

            const [submitCustomerDetailsResult] = await Promise.all([
                submitCustomerDetails({
                    token: submitAgreementResult.token,
                    fields: applicantFields,
                    customerKind,
                    sameCorrespondenceAddress: prefill,
                    customerCiamId: values.customerCiamId,
                    isCustomerSearchPerformed: values.isCustomerSearchPerformed,
                }),
            ]);

            await uploadOcrFiles(submitCustomerDetailsResult.token);

            if (customerAssetArray.length > 0) {
                await uploadApplicationDocuments(submitCustomerDetailsResult.token, customerAssetArray);
                await uploadLeadDocuments(submitCustomerDetailsResult.token, customerAssetArray);
            }

            const { appointmentModule, visitAppointmentModule } = retrieveAppointmentModule(
                application.event.module.__typename === 'EventApplicationModule' &&
                    (application.event.module as EventApplicationModuleDebugJourneyFragment)
            );

            const { hasShowroomVisit, hasTestDrive } = retrieveAppointmentScenarios(
                application.event.module.__typename === 'EventApplicationModule' &&
                    (application.event.module as EventApplicationModuleDebugJourneyFragment),
                application.event
            );

            if (hasTestDrive && values.configuration.testDrive) {
                const result = await submitAppointment(
                    temporarilyJourneyResult.token,
                    appointment,
                    appointmentModule,
                    application?.event?.displayAppointmentDatepicker,
                    eventModule.company.timeZone,
                    event
                );
                temporarilyJourneyResult = result;
            }

            if (hasShowroomVisit && values.configuration.visitAppointment) {
                const result = await submitVisitAppointment(
                    temporarilyJourneyResult.token,
                    visitAppointment,
                    visitAppointmentModule,
                    true,
                    eventModule.company.timeZone
                );
                temporarilyJourneyResult = result;
            }

            notification.destroy('primary');

            // go to the journey
            navigate('../apply', { state: { token: temporarilyJourneyResult.token } });
        },
        [
            notification,
            t,
            submitDraft,
            customerKind,
            submitAgreements,
            submitCustomerDetails,
            prefill,
            uploadOcrFiles,
            navigate,
            submitAppointment,
            eventModule.company.timeZone,
            event,
            submitVisitAppointment,
            updateApplicationFields,
            uploadApplicationDocuments,
            uploadLeadDocuments,
        ]
    );

    const { appointmentModule, visitAppointmentModule } = retrieveAppointmentModule(
        event.module.__typename === 'EventApplicationModule' &&
            (event.module as EventApplicationModuleDebugJourneyFragment)
    );

    const { data: bookedListing, loading } = useRetrieveBlockedAppointmentTimeSlotQuery({
        fetchPolicy: 'cache-and-network',
        variables: {
            moduleId: appointmentModule?.id,
        },
        skip: !appointmentModule?.id,
    });

    const bookedAppointmentTimeSlots = useMemo(
        () => bookedListing?.retrieveBlockedAppointmentTimeSlot || [],
        [bookedListing?.retrieveBlockedAppointmentTimeSlot]
    );

    const { data: visitbookedListing } = useRetrieveBlockedAppointmentTimeSlotQuery({
        fetchPolicy: 'cache-and-network',
        variables: {
            moduleId: visitAppointmentModule?.id,
        },
        skip: !visitAppointmentModule?.id,
    });

    const bookedVisitAppointmentTimeSlots = useMemo(
        () => visitbookedListing?.retrieveBlockedAppointmentTimeSlot || [],
        [visitbookedListing?.retrieveBlockedAppointmentTimeSlot]
    );

    if (loading) {
        return <PortalLoadingElement />;
    }

    return (
        <Inner
            appointmentModule={appointmentModule}
            bookedAppointmentTimeSlots={bookedAppointmentTimeSlots}
            bookedVisitAppointmentTimeSlots={bookedVisitAppointmentTimeSlots}
            onSubmit={onSubmit}
            visitAppointmentModule={visitAppointmentModule}
        />
    );
};

export default EventEntrypointInner;
