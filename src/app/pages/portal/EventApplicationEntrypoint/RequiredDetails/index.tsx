import dayjs from 'dayjs';
import { Formik } from 'formik';
import { pick } from 'lodash/fp';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router';
// eslint-disable-next-line max-len
import { EventApplicationModuleDebugJourneyFragment } from '../../../../api/fragments/EventApplicationModuleDebugJourney';
import { useRetrieveBlockedAppointmentTimeSlotQuery } from '../../../../api/queries/retrieveBlockedAppointmentTimeSlot';
import notification from '../../../../themes/porscheV3/notification';
import useHandleError from '../../../../utilities/useHandleError';
import useValidator from '../../../../utilities/useValidator';
import validators from '../../../../utilities/validators';
import {
    hasAppointmentScenario,
    hasVisitAppointmentScenario,
} from '../../../admin/ModuleDetailsPage/modules/implementations/shared/scenarios';
import { useFirstAvailableSlot } from '../../StandardApplicationEntrypoint/AppointmentPage/useAppointmentAvailability';
import useAppointmentSubmission from '../../StandardApplicationEntrypoint/AppointmentPage/useAppointmentSubmission';
import useUpdateAppointment from '../../StandardApplicationEntrypoint/AppointmentPage/useUpdateAppointment';
// eslint-disable-next-line max-len
import useUpdateVisitAppointment from '../../StandardApplicationEntrypoint/AppointmentPage/useUpdateVisitAppointment';
// eslint-disable-next-line max-len
import useVisitAppointmentSubmission from '../../StandardApplicationEntrypoint/AppointmentPage/useVisitAppointmentSubmission';
import { JourneyStage } from '../../StandardApplicationEntrypoint/Journey/shared';
import { retrieveAppointmentModule, retrieveAppointmentScenarios } from '../ApplicantForm/AppointmentDetailsSection';
import { EventJourneySetupProvider, useEventJourneySetupContext } from '../Entrypoint/EventJourneySetup';
import { usePersistEventJourneyValues } from '../Journey/usePersistEventJourneyValues';
import InnerForm from './InnerForm';
import type { RequiredDetailsFormValues, RequiredDetailsProps, StateAndDispatch } from './types';
import useSubmitRequiredDetails from './useSubmitRequiredDetails';
import useUpdateRequiredDetails from './useUpdateRequiredDetails';

const Inner = ({ dispatch, state }: StateAndDispatch) => {
    const { t } = useTranslation(['eventApplicantForm']);
    const navigate = useNavigate();
    const { save: persistEventJourneyValue, persistedValue: persistedEventValue } = usePersistEventJourneyValues();

    const { endpoint, event, eventModule, getDraftJourneyStages } = useEventJourneySetupContext();

    const { currentStage, availableStages } = useMemo(
        () => ({
            currentStage: state?.stage || JourneyStage.RequiredDetails,
            availableStages: state?.stages?.length ? state?.stages : getDraftJourneyStages(),
        }),
        [getDraftJourneyStages, state]
    );

    const { appointmentModule, visitAppointmentModule } = retrieveAppointmentModule(
        eventModule as EventApplicationModuleDebugJourneyFragment
    );

    const { data: bookedListing } = useRetrieveBlockedAppointmentTimeSlotQuery({
        fetchPolicy: 'cache-and-network',
        variables: {
            moduleId: appointmentModule?.id,
        },
        skip: !appointmentModule?.id,
    });

    const bookedAppointmentTimeSlots = useMemo(
        () => bookedListing?.retrieveBlockedAppointmentTimeSlot || [],
        [bookedListing?.retrieveBlockedAppointmentTimeSlot]
    );

    const appointmentDatePrefill = useFirstAvailableSlot(
        appointmentModule,
        bookedAppointmentTimeSlots,
        eventModule?.company?.timeZone,
        null,
        event
    );

    const { data: visitbookedListing } = useRetrieveBlockedAppointmentTimeSlotQuery({
        fetchPolicy: 'cache-and-network',
        variables: {
            moduleId: visitAppointmentModule?.id,
        },
        skip: !visitAppointmentModule?.id,
    });

    const bookedVisitAppointmentTimeSlots = useMemo(
        () => visitbookedListing?.retrieveBlockedAppointmentTimeSlot || [],
        [visitbookedListing?.retrieveBlockedAppointmentTimeSlot]
    );

    const visitDatePrefill = useFirstAvailableSlot(
        visitAppointmentModule,
        bookedVisitAppointmentTimeSlots,
        eventModule?.company?.timeZone
    );

    const vehicleValues = useMemo(() => {
        if (state?.application?.vehicle?.__typename === 'LocalVariant') {
            const {
                application: { vehicle },
            } = state;

            return {
                modelId: vehicle.model.parentModelId ? vehicle.model.parentModelId : vehicle.model.id,
                vehicleId: vehicle.id,
            };
        }

        return null;
    }, [state]);

    const configuration = useMemo(() => {
        if (!state?.application) {
            return {
                testDrive: (hasAppointmentScenario(event.scenarios) && event.scenarios.length === 1) || false,
                visitAppointment:
                    (hasVisitAppointmentScenario(event.scenarios) && event.scenarios.length === 1) || false,
                tradeIn: false,
            };
        }

        return pick(['testDrive', 'visitAppointment', 'tradeIn'], state.application.configuration);
    }, [event.scenarios, state?.application]);

    const { appointment, visitAppointment } = useMemo(() => {
        if (!state?.application) {
            return {
                appointment: {
                    date: appointmentDatePrefill?.date,
                    time: appointmentDatePrefill?.firstSlot?.value,
                    useCurrentDateTime: false,
                },
                visitAppointment: {
                    date: visitDatePrefill?.date,
                    time: visitDatePrefill?.firstSlot?.value,
                    useCurrentDateTime: false,
                },
            };
        }

        const {
            application: {
                appointmentStage,
                visitAppointmentStage,
                event: {
                    module: { company },
                },
            },
        } = state;

        const temporaryAppointment = persistedEventValue?.appointment;
        const temporaryVisitAppointment = persistedEventValue?.visitAppointment;

        const appointmentDetails = appointmentStage
            ? {
                  date: temporaryAppointment?.useCurrentDateTime
                      ? temporaryAppointment.date
                      : dayjs(appointmentStage.bookingTimeSlot?.slot).tz(company.timeZone),
                  time: temporaryAppointment?.useCurrentDateTime
                      ? temporaryAppointment.time
                      : dayjs(appointmentStage.bookingTimeSlot?.slot).tz(company.timeZone).format('HH:mm'),
                  useCurrentDateTime: temporaryAppointment?.useCurrentDateTime ?? false,
              }
            : temporaryAppointment;

        const visitAppointmentDetails = visitAppointmentStage
            ? {
                  date: temporaryVisitAppointment?.useCurrentDateTime
                      ? temporaryVisitAppointment.date
                      : dayjs(visitAppointmentStage.bookingTimeSlot?.slot).tz(company.timeZone),
                  time: temporaryVisitAppointment?.useCurrentDateTime
                      ? temporaryVisitAppointment.time
                      : dayjs(visitAppointmentStage.bookingTimeSlot?.slot).tz(company.timeZone).format('HH:mm'),
                  useCurrentDateTime: temporaryVisitAppointment?.useCurrentDateTime ?? false,
              }
            : temporaryVisitAppointment;

        return { appointment: appointmentDetails, visitAppointment: visitAppointmentDetails };
    }, [
        appointmentDatePrefill?.date,
        appointmentDatePrefill?.firstSlot?.value,
        persistedEventValue?.appointment,
        persistedEventValue?.visitAppointment,
        state,
        visitDatePrefill?.date,
        visitDatePrefill?.firstSlot?.value,
    ]);

    const dealerId = useMemo(() => {
        // if there is only one option available, set it as default
        if (event.dealerIds?.length === 1) {
            return event.dealerIds[0];
        }

        return null;
    }, [event.dealerIds]);

    const initialValues: RequiredDetailsFormValues = {
        dealerId: state?.application?.dealerId || dealerId,
        modelId: vehicleValues?.modelId,
        vehicleId: vehicleValues?.vehicleId,
        configuration,
        appointment,
        visitAppointment,
    };

    const submitRequiredDetails = useSubmitRequiredDetails(event.id, endpoint, {
        userIds: event.userIds,
        isPrivateAccess: event.privateAccess,
    });
    const updateRequiredDetails = useUpdateRequiredDetails(state);
    const submitAppointment = useAppointmentSubmission();
    const updateAppointment = useUpdateAppointment();
    const submitVisitAppointment = useVisitAppointmentSubmission();
    const updateVisitAppointment = useUpdateVisitAppointment();

    const onSubmit = useHandleError(
        async ({ appointment, visitAppointment, ...values }: RequiredDetailsFormValues) => {
            const isNew = !state?.application;

            notification.loading({
                content: t(`eventApplicantForm:messages.${isNew ? 'creationSubmitting' : 'submitting'}`),
                duration: 0,
                key: 'primary',
            });

            const submissionResult = isNew ? await submitRequiredDetails(values) : await updateRequiredDetails(values);
            const { application, token } = submissionResult.__typename === 'ApplicationJourney' && submissionResult;

            if (application.__typename !== 'EventApplication') {
                throw new Error('ApplicationKind not found');
            }

            const { appointmentModule, visitAppointmentModule } = retrieveAppointmentModule(
                application.event.module.__typename === 'EventApplicationModule' &&
                    (application.event.module as EventApplicationModuleDebugJourneyFragment)
            );

            const { hasShowroomVisit, hasTestDrive } = retrieveAppointmentScenarios(
                application.event.module.__typename === 'EventApplicationModule' &&
                    (application.event.module as EventApplicationModuleDebugJourneyFragment),
                application.event
            );

            let temporarilyJourneyResult = { application, token };
            if (hasTestDrive && values.configuration.testDrive) {
                const result = await submitAppointment(
                    token,
                    appointment,
                    appointmentModule,
                    application?.event?.displayAppointmentDatepicker,
                    eventModule.company.timeZone,
                    event
                );

                if (result.application.__typename !== 'EventApplication') {
                    throw new Error('ApplicationKind not found');
                }

                temporarilyJourneyResult = { application: result.application, token: result.token };
            }

            if (
                hasTestDrive &&
                values.configuration.testDrive &&
                appointmentModule.__typename === 'AppointmentModule' &&
                application.appointmentStage &&
                application.appointmentStage?.bookingTimeSlot?.slot
            ) {
                const newDate = dayjs(appointment.date).format('YYYY-MM-DD');
                const newTimeSlot = dayjs
                    .tz(`${newDate} ${appointment.time}`, appointmentModule.company.timeZone)
                    .utc()
                    .format('YYYY-MM-DDTHH:mm:ss.SSS[Z]');
                const selectedTimeSlot = application.appointmentStage.bookingTimeSlot.slot;
                if (newTimeSlot !== selectedTimeSlot) {
                    await updateAppointment(
                        token,
                        appointment,
                        appointmentModule,
                        eventModule.displayAppointmentDatepicker,
                        eventModule.company.timeZone
                    );

                    if (temporarilyJourneyResult.application?.appointmentStage?.bookingTimeSlot) {
                        temporarilyJourneyResult.application.appointmentStage.bookingTimeSlot.slot = newTimeSlot;
                    }
                }
            }

            if (hasShowroomVisit && values.configuration.visitAppointment) {
                const result = await submitVisitAppointment(
                    token,
                    visitAppointment,
                    visitAppointmentModule,
                    true,
                    eventModule.company.timeZone
                );

                if (result.application.__typename !== 'EventApplication') {
                    throw new Error('ApplicationKind not found');
                }

                temporarilyJourneyResult = { application: result.application, token: result.token };
            }

            if (
                hasShowroomVisit &&
                values.configuration.visitAppointment &&
                visitAppointmentModule.__typename === 'VisitAppointmentModule' &&
                application.visitAppointmentStage &&
                application.visitAppointmentStage?.bookingTimeSlot?.slot
            ) {
                const newDate = dayjs(visitAppointment.date).format('YYYY-MM-DD');
                const newTimeSlot = dayjs
                    .tz(`${newDate} ${visitAppointment.time}`, visitAppointmentModule.company.timeZone)
                    .utc()
                    .format('YYYY-MM-DDTHH:mm:ss.SSS[Z]');
                const selectedTimeSlot = application.visitAppointmentStage.bookingTimeSlot.slot;
                if (newTimeSlot !== selectedTimeSlot) {
                    await updateVisitAppointment(
                        token,
                        visitAppointment,
                        visitAppointmentModule,
                        eventModule.displayVisitAppointmentDatepicker,
                        eventModule.company.timeZone
                    );

                    if (temporarilyJourneyResult.application?.visitAppointmentStage?.bookingTimeSlot) {
                        temporarilyJourneyResult.application.visitAppointmentStage.bookingTimeSlot.slot = newTimeSlot;
                    }
                }
            }

            persistEventJourneyValue({
                ...persistedEventValue,
                appointment,
                visitAppointment,
            });

            notification.destroy('primary');

            if (isNew) {
                // go to the journey
                navigate('../apply', { state: { token: temporarilyJourneyResult.token } });
            } else {
                dispatch({
                    type: 'refresh',
                    application: temporarilyJourneyResult.application,
                    token: temporarilyJourneyResult.token,
                });
                dispatch({ type: 'goTo', stage: JourneyStage.ApplicantKYC });
            }
        },
        [
            state?.application,
            t,
            submitRequiredDetails,
            updateRequiredDetails,
            updateAppointment,
            persistEventJourneyValue,
            persistedEventValue,
            submitAppointment,
            eventModule.company.timeZone,
            event,
            submitVisitAppointment,
            navigate,
            dispatch,
        ]
    );

    const validate = useValidator(
        validators.compose(
            validators.requiredObjectId('dealerId'),
            validators.only(() => event.hasVehicleIntegration, validators.requiredObjectId('vehicleId')),
            validators.requiredBoolean('configuration.testDrive'),
            validators.requiredBoolean('configuration.visitAppointment'),
            validators.requiredBoolean('configuration.tradeIn'),
            validators.only(
                values => values.configuration.testDrive && values.appointment?.useCurrentDateTime,
                validators.compose(
                    validators.requiredDate('appointment.date'),
                    validators.requiredString('appointment.time')
                )
            ),
            validators.only(
                values => values.configuration.visitAppointment && values.visitAppointment?.useCurrentDateTime,
                validators.compose(
                    validators.requiredDate('visitAppointment.date'),
                    validators.requiredString('visitAppointment.time')
                )
            )
        )
    );

    return (
        <Formik initialValues={initialValues} onSubmit={onSubmit} validate={validate} validateOnMount>
            <InnerForm stage={currentStage} stages={availableStages} />
        </Formik>
    );
};

const RequiredDetails = ({ dispatch, endpoint, event, state }: RequiredDetailsProps) => (
    <EventJourneySetupProvider endpoint={endpoint} event={event}>
        <Inner dispatch={dispatch} state={state} />
    </EventJourneySetupProvider>
);

export default RequiredDetails;
