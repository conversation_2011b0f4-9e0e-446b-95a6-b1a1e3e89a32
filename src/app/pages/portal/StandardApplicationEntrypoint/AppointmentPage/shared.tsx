/* eslint-disable max-len */
import { PageContainerProps } from '@ant-design/pro-layout';
import { Dayjs } from 'dayjs';
import { isNil } from 'lodash/fp';
import { ComponentType, Dispatch, PropsWithChildren } from 'react';
import { ApplicationScenario } from '../../../../api';
import {
    AppointmentModuleApplicationJourneyFragment,
    AppointmentModuleSpecsFragment,
    ConfiguratorApplicationModuleDebugJourneyFragment,
    EventApplicationModuleDebugJourneyFragment,
    FinderApplicationEntrypointContextDataFragment,
    FinderApplicationPrivateModuleDebugJourneyFragment,
    FinderApplicationPublicModuleDebugJourneyFragment,
    JourneyEventDataFragment,
    StandardApplicationModuleDebugJourneyFragment,
    VisitAppointmentModuleSpecsFragment,
} from '../../../../api/fragments';
import { ConfiguratorApplicationEntrypointContextDataFragment } from '../../../../api/fragments/ConfiguratorApplicationEntrypointContextData';
import { DebugJourneyDataFragment } from '../../../../api/fragments/DebugJourneyData';
import { EventApplicationEntrypointContextDataFragment } from '../../../../api/fragments/EventApplicationEntrypointContextData';
import { FinderApplicationPublicAccessEntrypointContextDataFragment } from '../../../../api/fragments/FinderApplicationPublicAccessEntrypointContextData';
import { LaunchpadApplicationModuleDebugJourneyFragment } from '../../../../api/fragments/LaunchpadApplicationModuleDebugJourney';
import { StandardApplicationEntrypointContextDataFragment } from '../../../../api/fragments/StandardApplicationEntrypointContextData';
import type { ConfiguratorApplicationState } from '../../ConfiguratorApplicationEntrypoint/Journey/shared';
import type { EventApplicationState } from '../../EventApplicationEntrypoint/Journey/shared';
import type { FinderApplicationState } from '../../FinderApplicationPublicAccessEntrypoint/shared';
import type { StandardApplicationState } from '../Journey/shared';
import { Action, State } from '../Journey/shared';
import { AppointmentValues } from './appointmentValues';
import { useAppointmentSubmission } from './useAppointmentSubmission';
import useUpdateAppointment from './useUpdateAppointment';
import { useVisitAppointmentSubmission } from './useVisitAppointmentSubmission';

export const leftColSpan = { xl: 8, lg: 12, md: 24 };
export const rightColSpan = { xl: 16, lg: 12, mid: 24 };

export type ApplicationModuleFragment =
    | StandardApplicationModuleDebugJourneyFragment
    | FinderApplicationPublicModuleDebugJourneyFragment
    | FinderApplicationPrivateModuleDebugJourneyFragment
    | EventApplicationModuleDebugJourneyFragment
    | ConfiguratorApplicationModuleDebugJourneyFragment;

type HasTestDriveOrAppointment = {
    appointmentModule: AppointmentModuleApplicationJourneyFragment;
    visitAppointmentModule: VisitAppointmentModuleSpecsFragment;
    hasTestDrive: boolean;
    hasShowroomVisit: boolean;
};

export const getAppointmentModule = (
    module: ApplicationModuleFragment,
    event?: JourneyEventDataFragment
): HasTestDriveOrAppointment => {
    switch (module.__typename) {
        case 'ConfiguratorModule':
        case 'FinderApplicationPublicModule':
        case 'FinderApplicationPrivateModule':
        case 'StandardApplicationModule': {
            return {
                hasTestDrive: module.scenarios.includes(ApplicationScenario.Appointment),
                appointmentModule:
                    module.appointmentModule?.__typename === 'AppointmentModule' && module.appointmentModule,
                hasShowroomVisit: module.scenarios.includes(ApplicationScenario.VisitAppointment),
                visitAppointmentModule:
                    module.visitAppointmentModule?.__typename === 'VisitAppointmentModule' &&
                    (module.visitAppointmentModule as VisitAppointmentModuleSpecsFragment),
            };
        }
        case 'EventApplicationModule': {
            if (isNil(event)) {
                return null;
            }

            return {
                hasTestDrive: event.scenarios.includes(ApplicationScenario.Appointment),
                appointmentModule:
                    module.appointmentModule?.__typename === 'AppointmentModule' && module.appointmentModule,
                hasShowroomVisit: event.scenarios.includes(ApplicationScenario.VisitAppointment),
                visitAppointmentModule:
                    module.visitAppointmentModule?.__typename === 'VisitAppointmentModule' &&
                    (module.visitAppointmentModule as VisitAppointmentModuleSpecsFragment),
            };
        }

        default:
            throw new Error('ApplicationEntrypoint not supported');
    }
};

export type AllowedApplicationEndpoint =
    | StandardApplicationEntrypointContextDataFragment
    | ConfiguratorApplicationEntrypointContextDataFragment
    | EventApplicationEntrypointContextDataFragment
    | FinderApplicationPublicAccessEntrypointContextDataFragment
    | FinderApplicationEntrypointContextDataFragment;

export type AllowedApplicationForAppointment =
    | StandardApplicationState
    | EventApplicationState
    | ConfiguratorApplicationState
    | FinderApplicationState;

export type AppointmentPageProps = {
    state: State<AllowedApplicationForAppointment>;
    dispatch: Dispatch<Action<AllowedApplicationForAppointment>>;
    endpoint: AllowedApplicationEndpoint;
    CustomLayout?: ComponentType<PropsWithChildren<PageContainerProps>>;
    shouldIncludeLayout?: boolean;
};

export const ensureApplicationForAppointment = (application: DebugJourneyDataFragment['application']) => {
    switch (application.__typename) {
        case 'StandardApplication':
            return application as StandardApplicationState;

        case 'ConfiguratorApplication':
            return application as ConfiguratorApplicationState;

        case 'EventApplication':
            return application as EventApplicationState;

        case 'FinderApplication':
            return application as FinderApplicationState;

        default:
            throw new Error('Application not supported for appointment');
    }
};

export const ensureApplicationModuleForAppointment = (application: DebugJourneyDataFragment['application']) => {
    switch (application.module.__typename) {
        case 'StandardApplicationModule':
            return application.module as
                | StandardApplicationModuleDebugJourneyFragment
                | StandardApplicationEntrypointContextDataFragment;

        case 'ConfiguratorModule':
            return application.module as
                | ConfiguratorApplicationModuleDebugJourneyFragment
                | ConfiguratorApplicationEntrypointContextDataFragment;

        case 'EventApplicationModule':
            return application.module as
                | EventApplicationModuleDebugJourneyFragment
                | EventApplicationEntrypointContextDataFragment;

        case 'FinderApplicationPrivateModule':
            return application.module as
                | FinderApplicationPrivateModuleDebugJourneyFragment
                | FinderApplicationEntrypointContextDataFragment;

        case 'FinderApplicationPublicModule':
            return application.module as
                | FinderApplicationPublicModuleDebugJourneyFragment
                | FinderApplicationPublicAccessEntrypointContextDataFragment;

        case 'LaunchPadModule':
            return application.module as LaunchpadApplicationModuleDebugJourneyFragment;

        default:
            throw new Error('Application not supported for appointment');
    }
};

export const useAppointmentSubmissions = () => {
    /**
     * existing `submitAppointment` is test drive appointment
     * ::
     * submitVisitAppointment is only serve for `Showroom Visit`
     */
    const submitAppointment = useAppointmentSubmission();
    const updateAppointment = useUpdateAppointment();

    return {
        submitAppointment,
        updateAppointment,
    };
};

export const submitVisitAppointmentOrTestDrive = (
    submitAppointment: ReturnType<typeof useAppointmentSubmission>,
    submitVisitAppointment: ReturnType<typeof useVisitAppointmentSubmission>,
    token: string,
    values: AppointmentValues,
    timeZone: string,
    appointmentModule: AppointmentModuleSpecsFragment | VisitAppointmentModuleSpecsFragment,
    displayAppointmentDatepicker: boolean = true
) =>
    appointmentModule.__typename === 'AppointmentModule'
        ? submitAppointment(token, values, appointmentModule, displayAppointmentDatepicker, timeZone)
        : submitVisitAppointment(token, values, appointmentModule, displayAppointmentDatepicker, timeZone);
