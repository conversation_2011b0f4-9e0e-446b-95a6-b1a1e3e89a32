import { Formik } from 'formik';
import { isEmpty } from 'lodash/fp';
import { useMemo } from 'react';
import styled from 'styled-components';
import {
    ConfiguratorApplicationModuleDebugJourneyFragment,
    FinderApplicationPrivateModuleDebugJourneyFragment,
    FinderApplicationPublicModuleDebugJourneyFragment,
    StandardApplicationModuleDebugJourneyFragment,
} from '../../../../api';
import { useRetrieveBlockedAppointmentTimeSlotQuery } from '../../../../api/queries/retrieveBlockedAppointmentTimeSlot';
import PortalLoadingElement from '../../../../components/PortalLoadingElement';
import useHandleError from '../../../../utilities/useHandleError';
// eslint-disable-next-line max-len
import { retrieveAppointmentModuleFromEntrypoint } from '../../EventApplicationEntrypoint/ApplicantForm/AppointmentDetailsSection';
import type { State } from '../Journey/shared';
import Inner from './Inner';
import { AppointmentValues } from './appointmentValues';
import {
    AllowedApplicationForAppointment,
    AppointmentPageProps,
    ensureApplicationForAppointment,
    submitVisitAppointmentOrTestDrive,
    useAppointmentSubmissions,
} from './shared';
import { useFirstAvailableSlot } from './useAppointmentAvailability';
import useVisitAppointmentSubmission from './useVisitAppointmentSubmission';

export const StyledContainer = styled.div`
    & .ant-pro-page-container-children-content {
        padding: 0;
    }
`;

export const retrieveApplicationModuleFromState = (state: State<AllowedApplicationForAppointment>) => {
    const { module } = state.application;

    switch (module.__typename) {
        case 'StandardApplicationModule': {
            return state.application.module as StandardApplicationModuleDebugJourneyFragment;
        }

        case 'FinderApplicationPrivateModule': {
            return state.application.module as FinderApplicationPrivateModuleDebugJourneyFragment;
        }

        case 'FinderApplicationPublicModule': {
            return state.application.module as FinderApplicationPublicModuleDebugJourneyFragment;
        }

        case 'ConfiguratorModule': {
            return state.application.module as ConfiguratorApplicationModuleDebugJourneyFragment;
        }

        default:
            return null;
    }
};

const AppointmentPage = ({ state, dispatch, CustomLayout, endpoint, shouldIncludeLayout }: AppointmentPageProps) => {
    const { token, application } = state;

    const { appointmentModule, visitAppointmentModule } = retrieveAppointmentModuleFromEntrypoint(endpoint);

    const { data: bookedListing, loading: bookedLoading } = useRetrieveBlockedAppointmentTimeSlotQuery({
        fetchPolicy: 'cache-and-network',
        variables: {
            moduleId: appointmentModule?.id,
        },
        skip: isEmpty(appointmentModule?.id),
    });
    const bookedAppointmentTimeSlots = useMemo(
        () => bookedListing?.retrieveBlockedAppointmentTimeSlot || [],
        [bookedListing?.retrieveBlockedAppointmentTimeSlot]
    );

    const { data: visitBookedListing, loading: visitLoading } = useRetrieveBlockedAppointmentTimeSlotQuery({
        fetchPolicy: 'cache-and-network',
        variables: {
            moduleId: visitAppointmentModule?.id,
        },
        skip: isEmpty(visitAppointmentModule?.id),
    });
    const bookedVisitAppointmentTimeSlots = useMemo(
        () => visitBookedListing?.retrieveBlockedAppointmentTimeSlot || [],
        [visitBookedListing?.retrieveBlockedAppointmentTimeSlot]
    );

    const { data, loading } = useRetrieveBlockedAppointmentTimeSlotQuery({
        fetchPolicy: 'cache-and-network',
        variables: {
            moduleId: appointmentModule?.id,
        },
        skip: !appointmentModule?.id,
    });

    const timeZone = useMemo(
        () =>
            endpoint.__typename === 'StandardApplicationEntrypoint'
                ? endpoint.applicationModule.company.timeZone
                : undefined,
        [endpoint]
    );

    const datePrefill = useFirstAvailableSlot(
        appointmentModule,
        bookedAppointmentTimeSlots,
        timeZone,
        application?.appointmentStage?.bookingTimeSlot?.slot
    );

    const { submitAppointment } = useAppointmentSubmissions();
    const submitVisitAppointment = useVisitAppointmentSubmission();

    const onSubmit = useHandleError<AppointmentValues>(
        async values => {
            const result = await submitVisitAppointmentOrTestDrive(
                submitAppointment,
                submitVisitAppointment,
                token,
                values,
                timeZone,
                appointmentModule
            );

            dispatch({
                type: 'next',
                token: result.token,
                application: ensureApplicationForAppointment(result.application),
            });
        },
        [appointmentModule, dispatch, token, timeZone, submitAppointment, submitVisitAppointment]
    );

    const initialValues: AppointmentValues = useMemo(
        () => ({
            date: datePrefill.date ?? null,
            time: datePrefill.firstSlot?.value ?? null,
            useCurrentDateTime: false,
        }),
        [datePrefill.date, datePrefill.firstSlot?.value]
    );

    if (loading) {
        return <PortalLoadingElement />;
    }

    return (
        <Formik initialValues={initialValues} onSubmit={onSubmit}>
            <Inner
                CustomLayout={CustomLayout}
                bookedAppointmentTimeSlots={bookedAppointmentTimeSlots}
                bookedVisitAppointmentTimeSlots={bookedVisitAppointmentTimeSlots}
                dispatch={dispatch}
                endpoint={endpoint}
                shouldIncludeLayout={shouldIncludeLayout}
                state={state}
            />
        </Formik>
    );
};

export default AppointmentPage;
