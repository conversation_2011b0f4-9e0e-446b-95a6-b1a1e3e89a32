import { Col, Row, Typography } from 'antd';
import { useFormikContext } from 'formik';
import { isEmpty } from 'lodash/fp';
import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
    type CheckboxConsentsAndDeclarationsSettings,
    type ConditionSettings,
    LegalTextPosition,
} from '../../../api/types';
import TranslatedTextAreaField from '../../../components/fields/TranslatedTextAreaField';
import CollapsibleWrapper, { Panel } from '../../../components/wrappers/CollapsibleWrapper';
import FormFields from '../../../themes/admin/Fields/FormFields';
import useConsentsAndDeclarationsOptions from '../../../utilities/useConsentsAndDeclarationsOptions';
import useDataFieldOptions from '../../../utilities/useDataFieldOptions';
import useSystemSwitchData from '../../../utilities/useSystemSwitchData';
import useTooltip from '../../../utilities/useTooltip';
import ConditionsDisplay from './Conditions/ConditionsDisplay';

export type CheckboxConsentFormValues = CheckboxConsentsAndDeclarationsSettings & {
    conditionSettings: ConditionSettings[];
};

export type CheckboxConsentsAndDeclarationsDetailsProps = {
    initialValues: CheckboxConsentFormValues;
    updated?: string;
    offset?: string;
    disabled?: boolean;
};

const colSpan = { lg: 8, md: 12, xs: 24 };

const CheckboxConsentsAndDeclarationsDetails = ({
    updated,
    offset,
    disabled = false,
}: CheckboxConsentsAndDeclarationsDetailsProps) => {
    const { t } = useTranslation('consentsAndDeclarations');
    const { values, setFieldValue } = useFormikContext<CheckboxConsentFormValues>();
    const { yesNoSwitch } = useSystemSwitchData();
    const { legalTextPositionOptions } = useConsentsAndDeclarationsOptions();
    const dataFieldOptions = useDataFieldOptions();
    const legalTextPositionTooltip = useTooltip('tooltips.legalTextPosition', 'consentsAndDeclarations');
    // Not using translated string as it depends on system's language
    const hasLegalText = !isEmpty(values.legalMarkup?.defaultValue) || !isEmpty(values.legalMarkup?.overrides);

    useEffect(() => {
        if (!values.isMandatory) {
            setFieldValue('isMandatory', false);
        }
        if (!values.legalMarkup) {
            setFieldValue('legalMarkup', { defaultValue: '', overrides: [] });
        }
        if (!values.legalTextPosition) {
            setFieldValue('legalTextPosition', LegalTextPosition.Modal);
        }
        setFieldValue('defaultChecked', undefined);
        setFieldValue('platform', undefined);
    }, [setFieldValue, values.isMandatory, values.legalMarkup, values.legalTextPosition]);

    useEffect(() => {
        setFieldValue('legalTextPosition', hasLegalText ? LegalTextPosition.Modal : null);
    }, [hasLegalText, setFieldValue]);

    return (
        <CollapsibleWrapper
            defaultActiveKey={['consentsAndDeclarations_Configuration', 'consentsAndDeclarations_Conditions']}
        >
            <Panel
                key="consentsAndDeclarations_Configuration"
                header={
                    <Typography.Title level={5}>
                        {t('consentsAndDeclarations:cardTitles.configuration')}
                    </Typography.Title>
                }
            >
                <Row gutter={14}>
                    <Col {...colSpan}>
                        <FormFields.TranslatedInputField
                            {...t('consentsAndDeclarations:fields.title', {
                                returnObjects: true,
                            })}
                            disabled={disabled}
                            name="title"
                            required={
                                isEmpty(values.description.defaultValue) || values.description.defaultValue === ''
                            }
                        />
                    </Col>
                    <Col {...colSpan}>
                        <FormFields.SelectField
                            {...t('consentsAndDeclarations:fields.dataField', {
                                returnObjects: true,
                            })}
                            disabled={disabled}
                            name="dataField"
                            options={dataFieldOptions}
                            required
                            showSearch
                        />
                    </Col>
                    <Col {...colSpan}>
                        <FormFields.SwitchField
                            {...t('consentsAndDeclarations:fields.isMandatory', {
                                returnObjects: true,
                            })}
                            disabled={disabled}
                            name="isMandatory"
                            {...yesNoSwitch}
                        />
                    </Col>
                    <Col {...colSpan}>
                        <TranslatedTextAreaField
                            {...t('consentsAndDeclarations:fields.description', {
                                returnObjects: true,
                            })}
                            disabled={disabled}
                            name="description"
                            required={isEmpty(values.title.defaultValue) || values.title.defaultValue === ''}
                        />
                    </Col>
                    <Col {...colSpan}>
                        <TranslatedTextAreaField
                            {...t('consentsAndDeclarations:fields.legalMarkup', {
                                returnObjects: true,
                            })}
                            disabled={disabled}
                            name="legalMarkup"
                            onChange={e => {
                                const value = e?.target?.value ?? '';
                                if (!value) {
                                    setFieldValue('legalTextPosition', null);
                                }
                            }}
                        />
                    </Col>
                    <Col {...colSpan}>
                        <FormFields.SelectField
                            {...t('consentsAndDeclarations:fields.legalTextPosition', {
                                returnObjects: true,
                            })}
                            disabled={disabled || !hasLegalText}
                            name="legalTextPosition"
                            options={legalTextPositionOptions}
                            tooltip={legalTextPositionTooltip}
                            showSearch
                        />
                    </Col>
                    {updated && (
                        <Col {...colSpan}>
                            <FormFields.DisplayFieldWithUTCOffset
                                {...t('common:fields.updated', { returnObjects: true, offset })}
                                value={updated}
                            />
                        </Col>
                    )}
                </Row>
            </Panel>
            <Panel
                key="consentsAndDeclarations_Conditions"
                header={
                    <Typography.Title level={5}>{t('consentsAndDeclarations:cardTitles.conditions')}</Typography.Title>
                }
            >
                <ConditionsDisplay disabled={disabled} name="conditionSettings" />
            </Panel>
        </CollapsibleWrapper>
    );
};

export default CheckboxConsentsAndDeclarationsDetails;
