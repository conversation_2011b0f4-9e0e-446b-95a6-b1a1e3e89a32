import { ExclamationCircleOutlined } from '@ant-design/icons';
import { useApolloClient } from '@apollo/client';
import { Button, Form, Grid, message, Modal } from 'antd';
import { Formik } from 'formik';
import { pick } from 'lodash/fp';
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router';
import getApolloErrors from '../../../../server/utils/getApolloErrors';
import * as permissionKind from '../../../../shared/permissions';
// eslint-disable-next-line max-len
import { ConsentsAndDeclarationsWithPermissionsSpecsFragment } from '../../../api/fragments/ConsentsAndDeclarationsWithPermissionsSpecs';
import {
    DeleteConsentsAndDeclarationsDocument,
    DeleteConsentsAndDeclarationsMutation,
    DeleteConsentsAndDeclarationsMutationVariables,
} from '../../../api/mutations/deleteConsentsAndDeclarations';
import {
    UpdateCheckboxConsentsAndDeclarationsDocument,
    UpdateCheckboxConsentsAndDeclarationsMutation,
    UpdateCheckboxConsentsAndDeclarationsMutationVariables,
} from '../../../api/mutations/updateCheckboxConsentsAndDeclarations';
import {
    UpdateGroupConsentsAndDeclarationsDocument,
    UpdateGroupConsentsAndDeclarationsMutation,
    UpdateGroupConsentsAndDeclarationsMutationVariables,
} from '../../../api/mutations/updateGroupConsentsAndDeclarations';
import {
    UpdateMarketingConsentsAndDeclarationsDocument,
    UpdateMarketingConsentsAndDeclarationsMutation,
    UpdateMarketingConsentsAndDeclarationsMutationVariables,
} from '../../../api/mutations/updateMarketingConsentsAndDeclarations';
import {
    UpdateTextConsentsAndDeclarationsDocument,
    UpdateTextConsentsAndDeclarationsMutation,
    UpdateTextConsentsAndDeclarationsMutationVariables,
} from '../../../api/mutations/updateTextConsentsAndDeclarations';
import { useGetModuleSpecsQuery } from '../../../api/queries/getModuleSpecs';
import type {
    CheckboxConsentsAndDeclarationsSettings,
    ConditionSettings,
    GroupConsentsAndDeclarationsSettings,
    MarketingConsentsAndDeclarationsSettings,
    TextConsentsAndDeclarationsSettings,
} from '../../../api/types';
import { ConsentsAndDeclarationsType, DataField } from '../../../api/types';
import ConsolePageWithHeader from '../../../layouts/ConsoleLayout/ConsolePageWithHeader';
import hasPermissions from '../../../utilities/hasPermissions';
import useFormattedSimpleVersioning from '../../../utilities/useFormattedSimpleVersioning';
import useHandleError from '../../../utilities/useHandleError';
import ConsentsAndDeclarationsDetails from './ConsentsAndDeclarationsDetails';
import getConditionSettings from './getConditionSettings';
import prepareSubmissionConditionSettings from './prepareSubmissionConditionSettings';
import { cndSettings } from './shared';
import useConsentsAndDeclarationsValidator from './useConsentsAndDeclarationsValidator';

export type ConsentsAndDeclarationsInnerPageProps = {
    consentAndDeclaration: ConsentsAndDeclarationsWithPermissionsSpecsFragment;
};

type FormValues = cndSettings & {
    conditionSettings: ConditionSettings[];
    moduleId: string;
    consentType: ConsentsAndDeclarationsType;
};

const ConsentsAndDeclarationsInnerPage = ({ consentAndDeclaration }: ConsentsAndDeclarationsInnerPageProps) => {
    const { t } = useTranslation(['consentsAndDeclarations']);
    const navigate = useNavigate();
    const validate = useConsentsAndDeclarationsValidator(true);
    const { type, moduleId } = consentAndDeclaration;
    const conditionSettings = useMemo(
        () => (consentAndDeclaration.conditions ? getConditionSettings(consentAndDeclaration.conditions) : []),
        [consentAndDeclaration.conditions]
    );

    const consentsAndDeclarationsSuiteId = consentAndDeclaration.versioning.suiteId;
    const { updated, offset } = useFormattedSimpleVersioning({
        versioning: consentAndDeclaration.versioning,
        moduleId: consentAndDeclaration.moduleId,
    });

    const apolloClient = useApolloClient();
    const screens = Grid.useBreakpoint();

    const onSubmit = useHandleError<FormValues>(
        async values => {
            const { moduleId, conditionSettings: conditionSettingsInput, consentType, ...settings } = values;
            const conditionSettings = prepareSubmissionConditionSettings(conditionSettingsInput);

            // ensure children not included for non-group types
            let sanitizedSettings = settings;
            if (consentType !== ConsentsAndDeclarationsType.Group && 'children' in sanitizedSettings) {
                const { children, ...rest } = sanitizedSettings;
                sanitizedSettings = rest;
            }

            // submitting message
            message.loading({
                content: t('consentsAndDeclarations:messages.updateSubmitting'),
                key: 'primary',
                duration: 0,
            });

            // submit update
            switch (consentType) {
                case ConsentsAndDeclarationsType.Text: {
                    const textCndSettings = sanitizedSettings as TextConsentsAndDeclarationsSettings;

                    await apolloClient
                        .mutate<
                            UpdateTextConsentsAndDeclarationsMutation,
                            UpdateTextConsentsAndDeclarationsMutationVariables
                        >({
                            mutation: UpdateTextConsentsAndDeclarationsDocument,
                            variables: {
                                suiteId: consentsAndDeclarationsSuiteId,
                                settings: textCndSettings,
                                conditionSettings,
                            },
                        })
                        .finally(() => {
                            message.destroy('primary');
                        });

                    break;
                }

                case ConsentsAndDeclarationsType.Checkbox: {
                    const checkboxCndSettings = sanitizedSettings as CheckboxConsentsAndDeclarationsSettings;

                    await apolloClient
                        .mutate<
                            UpdateCheckboxConsentsAndDeclarationsMutation,
                            UpdateCheckboxConsentsAndDeclarationsMutationVariables
                        >({
                            mutation: UpdateCheckboxConsentsAndDeclarationsDocument,
                            variables: {
                                suiteId: consentsAndDeclarationsSuiteId,
                                settings: checkboxCndSettings,
                                conditionSettings,
                            },
                        })
                        .finally(() => {
                            message.destroy('primary');
                        });

                    break;
                }

                case ConsentsAndDeclarationsType.Marketing: {
                    const marketingCndSettings = sanitizedSettings as MarketingConsentsAndDeclarationsSettings;

                    await apolloClient
                        .mutate<
                            UpdateMarketingConsentsAndDeclarationsMutation,
                            UpdateMarketingConsentsAndDeclarationsMutationVariables
                        >({
                            mutation: UpdateMarketingConsentsAndDeclarationsDocument,
                            variables: {
                                suiteId: consentsAndDeclarationsSuiteId,
                                settings: marketingCndSettings,
                                conditionSettings,
                            },
                        })
                        .finally(() => {
                            message.destroy('primary');
                        });

                    break;
                }

                case ConsentsAndDeclarationsType.Group: {
                    const rawGroupSettings = settings as GroupConsentsAndDeclarationsSettings;

                    const { displayName, description, orderNumber, isActive, purpose, children } = rawGroupSettings;

                    // Transform children to inherit parent properties and calculate orderNumber
                    const transformedChildren = (children || []).map(
                        (child: CheckboxConsentsAndDeclarationsSettings, index: number) => ({
                            ...child,
                            displayName, // Inherit parent displayName
                            orderNumber: index + 1, // Calculate based on position in array
                            isActive, // Inherit from parent
                            isMandatory: child.isMandatory ?? false,
                            purpose, // Inherit from parent
                            dataField: child.dataField || DataField.None,
                            hasLegalMarkup: !!(
                                child.legalMarkup?.defaultValue ||
                                (child.legalMarkup?.overrides && child.legalMarkup.overrides.length > 0)
                            ),
                            id: child.id || undefined,
                        })
                    );

                    const groupCndSettings: GroupConsentsAndDeclarationsSettings = {
                        displayName,
                        description: description || { defaultValue: '', overrides: [] },
                        dataField: DataField.None,
                        orderNumber,
                        isActive,
                        purpose,
                        children: transformedChildren,
                    };

                    await apolloClient
                        .mutate<
                            UpdateGroupConsentsAndDeclarationsMutation,
                            UpdateGroupConsentsAndDeclarationsMutationVariables
                        >({
                            mutation: UpdateGroupConsentsAndDeclarationsDocument,
                            variables: {
                                suiteId: consentsAndDeclarationsSuiteId,
                                settings: groupCndSettings,
                                conditionSettings,
                            },
                        })
                        .finally(() => {
                            message.destroy('primary');
                        });

                    break;
                }

                default:
                    throw new Error('Consents and declarations type is not supported');
            }

            // inform about success
            message.success({
                content: t('consentsAndDeclarations:messages.updateSuccessful'),
                key: 'primary',
            });
        },
        [t, type, apolloClient, consentsAndDeclarationsSuiteId]
    );

    const deleteConsentsAndDeclarations = useCallback(() => {
        Modal.confirm({
            className: 'static-modal',
            title: t('consentsAndDeclarations:deleteModal.title'),
            icon: <ExclamationCircleOutlined />,
            content: t('consentsAndDeclarations:deleteModal.content'),
            okText: t('consentsAndDeclarations:deleteModal.okText'),
            okType: 'danger',
            cancelText: t('consentsAndDeclarations:deleteModal.cancelText'),
            async onOk() {
                try {
                    // loading message
                    message.loading({
                        content: t('consentsAndDeclarations:message.deleteSubmitting'),
                        key: 'primary',
                        duration: 0,
                    });

                    // delete with API
                    await apolloClient
                        .mutate<DeleteConsentsAndDeclarationsMutation, DeleteConsentsAndDeclarationsMutationVariables>({
                            mutation: DeleteConsentsAndDeclarationsDocument,
                            variables: { suiteId: consentsAndDeclarationsSuiteId },
                        })
                        .finally(() => {
                            message.destroy('primary');
                        });

                    // show success
                    message.success({
                        content: t('consentsAndDeclarations:messages.deleteSuccessful'),
                        key: 'primary',
                    });

                    // then go back to list
                    navigate('/admin/consents');
                } catch (error) {
                    const apolloErrors = getApolloErrors(error);

                    if (apolloErrors?.$root) {
                        message.error(apolloErrors?.$root);
                    }
                }
            },
        });
    }, [apolloClient, t, consentsAndDeclarationsSuiteId, navigate]);

    const initialValues = useMemo((): FormValues => {
        switch (consentAndDeclaration.__typename) {
            case 'TextConsentsAndDeclarations': {
                const cndSettings = {
                    ...pick(
                        [
                            'displayName',
                            'title',
                            'orderNumber',
                            'isActive',
                            'description',
                            'moduleId',
                            'purpose',
                            'dataField',
                        ],
                        consentAndDeclaration
                    ),
                };

                return {
                    ...cndSettings,
                    consentType: type,
                    conditionSettings,
                };
            }

            case 'CheckboxConsentsAndDeclarations': {
                const cndSettings = {
                    ...pick(
                        [
                            'displayName',
                            'title',
                            'orderNumber',
                            'isMandatory',
                            'isActive',
                            'description',
                            'moduleId',
                            'purpose',
                            'legalMarkup',
                            'dataField',
                            'legalTextPosition',
                        ],
                        consentAndDeclaration
                    ),
                };

                return {
                    ...cndSettings,
                    consentType: type,
                    conditionSettings,
                };
            }

            case 'MarketingConsentsAndDeclarations': {
                const cndSettings = {
                    ...pick(
                        [
                            'displayName',
                            'title',
                            'orderNumber',
                            'isMandatory',
                            'isActive',
                            'description',
                            'moduleId',
                            'purpose',
                            'platform',
                            'dataField',
                            'defaultChecked',
                        ],
                        consentAndDeclaration
                    ),
                };

                return {
                    ...cndSettings,
                    consentType: type,
                    conditionSettings,
                };
            }

            case 'GroupConsentsAndDeclarations': {
                const baseGroupSettings = pick(
                    ['displayName', 'title', 'orderNumber', 'isActive', 'description', 'purpose', 'dataField'],
                    consentAndDeclaration
                );

                // Transform existing children from GraphQL format to form input format
                const existingChildren = consentAndDeclaration.children || [];
                const transformedChildren = existingChildren
                    .map((child, index: number) => {
                        if (child.__typename !== 'CheckboxConsentsAndDeclarations') {
                            return null;
                        }

                        return {
                            id: child.id,
                            title: child.title || { defaultValue: '', overrides: [] },
                            description: child.description || { defaultValue: '', overrides: [] },
                            isMandatory: child.isMandatory || false,
                            legalMarkup: child.legalMarkup || { defaultValue: '', overrides: [] },
                            legalTextPosition: child.legalTextPosition || null,
                            dataField: child.dataField || DataField.None,
                            hasLegalMarkup: !!child.legalMarkup?.defaultValue,
                            displayName: child.displayName || '',
                            orderNumber: child.orderNumber || index + 1,
                            isActive: child.isActive ?? true,
                            purpose: child.purpose || [],
                        };
                    })
                    .filter(Boolean);

                return {
                    ...baseGroupSettings,
                    consentType: type,
                    children: transformedChildren,
                    conditionSettings,
                    moduleId,
                };
            }

            default:
                throw new Error('Consents and declarations type is not supported');
        }
    }, [conditionSettings, consentAndDeclaration, moduleId, type]);

    const { data } = useGetModuleSpecsQuery({
        fetchPolicy: 'cache-and-network',
        variables: { moduleId },
    });

    if (!data) {
        return null;
    }

    return (
        <Formik initialValues={initialValues} onSubmit={onSubmit} validate={validate}>
            {({ handleSubmit }) => (
                <ConsolePageWithHeader
                    extra={
                        hasPermissions(consentAndDeclaration.permissions, [permissionKind.deleteAgreement]) && (
                            <Button onClick={deleteConsentsAndDeclarations} danger>
                                {t('consentsAndDeclarations:actions.delete')}
                            </Button>
                        )
                    }
                    footer={[
                        hasPermissions(consentAndDeclaration.permissions, [permissionKind.updateAgreement]) && (
                            <Button
                                key="consentsAndDeclarationsUpdate"
                                form="consentsAndDeclarationsUpdate"
                                htmlType="submit"
                                type="primary"
                            >
                                {t('consentsAndDeclarations:actions.update')}
                            </Button>
                        ),
                    ].filter(Boolean)}
                    onBack={() => navigate('/admin/consents')}
                    title={(() => {
                        const { displayName } = initialValues;

                        return screens.md
                            ? t('consentsAndDeclarations:detailTitle', { name: displayName })
                            : displayName;
                    })()}
                >
                    <Form
                        id="consentsAndDeclarationsUpdate"
                        layout="vertical"
                        name="consentsAndDeclarationsUpdate"
                        onSubmitCapture={handleSubmit}
                    >
                        <ConsentsAndDeclarationsDetails
                            disabled={
                                !hasPermissions(consentAndDeclaration.permissions, [permissionKind.updateAgreement])
                            }
                            initialValues={initialValues}
                            offset={offset}
                            updated={updated}
                        />
                    </Form>
                </ConsolePageWithHeader>
            )}
        </Formik>
    );
};

export default ConsentsAndDeclarationsInnerPage;
