import { ArrowDownOutlined, ArrowUpOutlined, DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import { Button, Col, Row, Space, Typography } from 'antd';
import { FieldArray, useFormikContext } from 'formik';
import { isEmpty } from 'lodash/fp';
import { useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import {
    type CheckboxConsentsAndDeclarationsSettings,
    type ConditionSettings,
    type GroupConsentsAndDeclarationsSettings,
    LegalTextPosition,
} from '../../../api/types';
import TranslatedTextAreaField from '../../../components/fields/TranslatedTextAreaField';
import CollapsibleWrapper, { Panel } from '../../../components/wrappers/CollapsibleWrapper';
import FormFields from '../../../themes/admin/Fields/FormFields';
import { useThemeComponents } from '../../../themes/hooks';
import useConsentsAndDeclarationsOptions from '../../../utilities/useConsentsAndDeclarationsOptions';
import useDataFieldOptions from '../../../utilities/useDataFieldOptions';
import useSystemSwitchData from '../../../utilities/useSystemSwitchData';
import useTooltip from '../../../utilities/useTooltip';
import ConditionsDisplay from './Conditions/ConditionsDisplay';

export type GroupConsentFormValues = GroupConsentsAndDeclarationsSettings & {
    conditionSettings: ConditionSettings[];
};

export type GroupConsentsAndDeclarationsDetailsProps = {
    disabled?: boolean;
};

const ConsentDetailsActionButton = styled(Button)`
    width: 46px;
    height: 24px;
`;

const colSpan = { lg: 8, md: 12, xs: 24 };

const ChildSettingsRow = ({
    child,
    index,
    disabled,
}: {
    child: CheckboxConsentsAndDeclarationsSettings;
    index: number;
    disabled: boolean;
}) => {
    const { t } = useTranslation('consentsAndDeclarations');
    const { values, setFieldValue } = useFormikContext<GroupConsentFormValues>();
    const dataFieldOptions = useDataFieldOptions();
    const { yesNoSwitch } = useSystemSwitchData();
    const { legalTextPositionOptions } = useConsentsAndDeclarationsOptions();
    const legalTextPositionTooltip = useTooltip('tooltips.legalTextPosition', 'consentsAndDeclarations');

    const childPrefix = `children.${index}`;
    const hasLegalText = !isEmpty(child.legalMarkup?.defaultValue) || !isEmpty(child.legalMarkup?.overrides);

    useEffect(() => {
        setFieldValue(`${childPrefix}.legalTextPosition`, hasLegalText ? LegalTextPosition.Modal : null);
    }, [childPrefix, hasLegalText, setFieldValue]);

    return (
        <Row gutter={14}>
            <Col {...colSpan}>
                <FormFields.TranslatedInputField
                    {...t('consentsAndDeclarations:fields.title', {
                        returnObjects: true,
                    })}
                    disabled={disabled}
                    name={`${childPrefix}.title`}
                    required={
                        isEmpty(values.children[index].description.defaultValue) ||
                        values.children[index].description.defaultValue === ''
                    }
                />
            </Col>
            <Col {...colSpan}>
                <FormFields.SelectField
                    {...t('consentsAndDeclarations:fields.dataField', {
                        returnObjects: true,
                    })}
                    disabled={disabled}
                    name={`${childPrefix}.dataField`}
                    options={dataFieldOptions}
                    required
                    showSearch
                />
            </Col>
            <Col {...colSpan}>
                <FormFields.SwitchField
                    {...t('consentsAndDeclarations:fields.isMandatory', {
                        returnObjects: true,
                    })}
                    disabled={disabled}
                    name={`${childPrefix}.isMandatory`}
                    {...yesNoSwitch}
                />
            </Col>
            <Col {...colSpan}>
                <TranslatedTextAreaField
                    {...t('consentsAndDeclarations:fields.description', {
                        returnObjects: true,
                    })}
                    disabled={disabled}
                    name={`${childPrefix}.description`}
                    required={
                        isEmpty(values.children[index].title.defaultValue) ||
                        values.children[index].title.defaultValue === ''
                    }
                />
            </Col>
            <Col {...colSpan}>
                <TranslatedTextAreaField
                    {...t('consentsAndDeclarations:fields.legalMarkup', {
                        returnObjects: true,
                    })}
                    disabled={disabled}
                    name={`${childPrefix}.legalMarkup`}
                />
            </Col>
            <Col {...colSpan}>
                <FormFields.SelectField
                    {...t('consentsAndDeclarations:fields.legalTextPosition', {
                        returnObjects: true,
                    })}
                    disabled={disabled || !hasLegalText}
                    name={`${childPrefix}.legalTextPosition`}
                    options={legalTextPositionOptions}
                    tooltip={legalTextPositionTooltip}
                    showSearch
                />
            </Col>
        </Row>
    );
};

const GroupConsentsAndDeclarationsDetails = ({ disabled = false }: GroupConsentsAndDeclarationsDetailsProps) => {
    const { t } = useTranslation('consentsAndDeclarations');
    const { Button } = useThemeComponents();
    const { values, setFieldValue } = useFormikContext<GroupConsentFormValues>();

    const createDefaultChild = useCallback(
        (): CheckboxConsentsAndDeclarationsSettings => ({
            displayName: '',
            title: { defaultValue: '', overrides: [] },
            description: { defaultValue: '', overrides: [] },
            orderNumber: 1,
            isActive: true,
            dataField: null,
            purpose: [],
            isMandatory: false,
            legalMarkup: { defaultValue: '', overrides: [] },
            legalTextPosition: null,
            hasLegalMarkup: false,
        }),
        []
    );

    const initializeDefaultChildren = useCallback(() => {
        if (!values.children || values.children.length === 0) {
            const defaultChildren = [createDefaultChild(), createDefaultChild()];
            setFieldValue('children', defaultChildren);
        }
    }, [values.children, setFieldValue, createDefaultChild]);

    useEffect(() => {
        initializeDefaultChildren();
    }, [initializeDefaultChildren]);

    const moveChild = useCallback(
        (fromIndex: number, toIndex: number) => {
            if (toIndex < 0 || toIndex >= values.children.length) {
                return;
            }

            const newChildren = [...values.children];
            const [movedItem] = newChildren.splice(fromIndex, 1);
            newChildren.splice(toIndex, 0, movedItem);
            setFieldValue('children', newChildren);
        },
        [values.children, setFieldValue]
    );

    return (
        <FieldArray name="children">
            {({ push, remove }) => (
                <>
                    <CollapsibleWrapper
                        activeKey={[
                            ...(values.children?.map(
                                (child, index) => `consentsAndDeclarations_ConsentDetails_${child.id || index}`
                            ) || []),
                            'consentsAndDeclarations_Conditions',
                        ]}
                    >
                        {values.children?.map((child, index) => (
                            <Panel
                                key={`consentsAndDeclarations_ConsentDetails_${child.id || index}`}
                                extra={
                                    <Space>
                                        {index < values.children.length - 1 && (
                                            <ConsentDetailsActionButton
                                                icon={<ArrowDownOutlined />}
                                                onClick={() => moveChild(index, index + 1)}
                                            />
                                        )}
                                        {index > 0 && (
                                            <ConsentDetailsActionButton
                                                icon={<ArrowUpOutlined />}
                                                onClick={() => moveChild(index, index - 1)}
                                            />
                                        )}
                                        {values.children.length > 2 && (
                                            <ConsentDetailsActionButton
                                                icon={<DeleteOutlined />}
                                                onClick={() => remove(index)}
                                            />
                                        )}
                                    </Space>
                                }
                                header={
                                    <Typography.Title level={5}>
                                        {t('consentsAndDeclarations:cardTitles.consentDetails')} {index + 1}
                                    </Typography.Title>
                                }
                            >
                                <ChildSettingsRow child={child} disabled={disabled} index={index} />
                            </Panel>
                        ))}
                    </CollapsibleWrapper>

                    <Button
                        disabled={disabled}
                        icon={<PlusOutlined />}
                        onClick={() => push(createDefaultChild())}
                        style={{ marginBottom: '16px' }}
                    >
                        {t('consentsAndDeclarations:actions.addMore')}
                    </Button>

                    <CollapsibleWrapper defaultActiveKey="consentsAndDeclarations_Conditions">
                        <Panel
                            key="consentsAndDeclarations_Conditions"
                            header={
                                <Typography.Title level={5}>
                                    {t('consentsAndDeclarations:cardTitles.conditions')}
                                </Typography.Title>
                            }
                        >
                            <ConditionsDisplay disabled={disabled} name="conditionSettings" />
                        </Panel>
                    </CollapsibleWrapper>
                </>
            )}
        </FieldArray>
    );
};

export default GroupConsentsAndDeclarationsDetails;
