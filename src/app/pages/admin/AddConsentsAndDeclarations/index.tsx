/* eslint-disable max-len */
import { useApolloClient } from '@apollo/client';
import { message } from 'antd';
import { Formik } from 'formik';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation, useNavigate } from 'react-router';
import {
    CreateCheckboxConsentsAndDeclarationsMutation,
    CreateCheckboxConsentsAndDeclarationsMutationVariables,
    CreateCheckboxConsentsAndDeclarationsDocument,
} from '../../../api/mutations/createCheckboxConsentsAndDeclarations';
import {
    CreateGroupConsentsAndDeclarationsMutation,
    CreateGroupConsentsAndDeclarationsMutationVariables,
    CreateGroupConsentsAndDeclarationsDocument,
} from '../../../api/mutations/createGroupConsentsAndDeclarations';
import {
    CreateMarketingConsentsAndDeclarationsMutation,
    CreateMarketingConsentsAndDeclarationsMutationVariables,
    CreateMarketingConsentsAndDeclarationsDocument,
} from '../../../api/mutations/createMarketingConsentsAndDeclarations';
import {
    CreateTextConsentsAndDeclarationsMutation,
    CreateTextConsentsAndDeclarationsMutationVariables,
    CreateTextConsentsAndDeclarationsDocument,
} from '../../../api/mutations/createTextConsentsAndDeclarations';
import type {
    TextConsentsAndDeclarationsSettings,
    CheckboxConsentsAndDeclarationsSettings,
    MarketingConsentsAndDeclarationsSettings,
    GroupConsentsAndDeclarationsSettings,
    ConditionSettings,
} from '../../../api/types';
import { ConsentsAndDeclarationsType, DataField } from '../../../api/types';
import useLoadingButton from '../../../components/button/useLoadingButton';
import Form from '../../../components/fields/Form';
import ConsolePageWithHeader from '../../../layouts/ConsoleLayout/ConsolePageWithHeader';
import useHandleError from '../../../utilities/useHandleError';
import ConsentsAndDeclarationsDetails from '../ConsentsAndDeclarationsDetailsPage/ConsentsAndDeclarationsDetails';
import prepareSubmissionConditionSettings from '../ConsentsAndDeclarationsDetailsPage/prepareSubmissionConditionSettings';
import useConsentsAndDeclarationsValidator from '../ConsentsAndDeclarationsDetailsPage/useConsentsAndDeclarationsValidator';

type cndSettings =
    | TextConsentsAndDeclarationsSettings
    | CheckboxConsentsAndDeclarationsSettings
    | MarketingConsentsAndDeclarationsSettings
    | GroupConsentsAndDeclarationsSettings;

type FormValues = cndSettings & {
    conditionSettings?: ConditionSettings[];
    moduleId?: string;
    consentType: ConsentsAndDeclarationsType;
};

const AddConsentsAndDeclarations = () => {
    const { t } = useTranslation(['consentsAndDeclarations']);
    const navigate = useNavigate();
    const { setLoading, LoadingButton } = useLoadingButton();
    const { state } = useLocation();
    const validate = useConsentsAndDeclarationsValidator(false);

    const apolloClient = useApolloClient();

    const onSubmit = useHandleError<FormValues>(
        async values => {
            const { moduleId, consentType, conditionSettings: conditionSettingsField, ...settings } = values;

            const conditionSettings = prepareSubmissionConditionSettings(conditionSettingsField);

            setLoading(true);
            message.loading({
                content: t('consentsAndDeclarations:messages.creationSubmitting'),
                duration: 0,
                key: 'primary',
            });
            // call mutation to create new consent and declaration
            switch (consentType) {
                case ConsentsAndDeclarationsType.Text: {
                    const textCndSettings = settings as TextConsentsAndDeclarationsSettings;

                    await apolloClient
                        .mutate<
                            CreateTextConsentsAndDeclarationsMutation,
                            CreateTextConsentsAndDeclarationsMutationVariables
                        >({
                            mutation: CreateTextConsentsAndDeclarationsDocument,
                            variables: {
                                moduleId,
                                settings: textCndSettings,
                                conditionSettings,
                                eventId: state?.eventId,
                            },
                        })
                        .finally(() => {
                            // clean up
                            message.destroy('primary');
                            setLoading(false);
                        });

                    break;
                }

                case ConsentsAndDeclarationsType.Checkbox: {
                    const checkboxCndSettings = settings as CheckboxConsentsAndDeclarationsSettings;

                    await apolloClient
                        .mutate<
                            CreateCheckboxConsentsAndDeclarationsMutation,
                            CreateCheckboxConsentsAndDeclarationsMutationVariables
                        >({
                            mutation: CreateCheckboxConsentsAndDeclarationsDocument,
                            variables: {
                                moduleId,
                                settings: checkboxCndSettings,
                                conditionSettings,
                                eventId: state?.eventId,
                            },
                        })
                        .finally(() => {
                            // clean up
                            message.destroy('primary');
                            setLoading(false);
                        });

                    break;
                }

                case ConsentsAndDeclarationsType.Marketing: {
                    const marketingCndSettings = settings as MarketingConsentsAndDeclarationsSettings;

                    await apolloClient
                        .mutate<
                            CreateMarketingConsentsAndDeclarationsMutation,
                            CreateMarketingConsentsAndDeclarationsMutationVariables
                        >({
                            mutation: CreateMarketingConsentsAndDeclarationsDocument,
                            variables: {
                                moduleId,
                                settings: marketingCndSettings,
                                conditionSettings,
                                eventId: state?.eventId,
                            },
                        })
                        .finally(() => {
                            // clean up
                            message.destroy('primary');
                            setLoading(false);
                        });

                    break;
                }

                case ConsentsAndDeclarationsType.Group: {
                    const { children, ...groupFields } = settings as GroupConsentsAndDeclarationsSettings;

                    // Transform children to inherit parent properties and calculate orderNumber
                    const transformedChildren = (children || []).map(
                        (child: CheckboxConsentsAndDeclarationsSettings, index: number) => ({
                            ...child,
                            orderNumber: index + 1, // Calculate based on position in array
                            isMandatory: !!child.isMandatory,
                            dataField: child.dataField,
                            hasLegalMarkup: !!child.legalMarkup?.defaultValue,
                            // Inherit parent
                            displayName: groupFields.displayName,
                            isActive: groupFields.isActive,
                            purpose: groupFields.purpose,
                        })
                    );

                    const groupCndSettings: GroupConsentsAndDeclarationsSettings = {
                        ...groupFields,
                        dataField: DataField.None,
                        children: transformedChildren,
                    };

                    await apolloClient
                        .mutate<
                            CreateGroupConsentsAndDeclarationsMutation,
                            CreateGroupConsentsAndDeclarationsMutationVariables
                        >({
                            mutation: CreateGroupConsentsAndDeclarationsDocument,
                            variables: {
                                moduleId,
                                settings: groupCndSettings,
                                conditionSettings,
                                eventId: state?.eventId,
                            },
                        })
                        .finally(() => {
                            // clean up
                            message.destroy('primary');
                            setLoading(false);
                        });

                    break;
                }

                default:
                    throw new Error('Consent and declaration type not supported');
            }

            // show successful message
            message.success({
                content: t('consentsAndDeclarations:messages.creationSuccessful'),
                key: 'primary',
            });
            setLoading(false);
            navigate(-1);
        },
        [setLoading, t, navigate, apolloClient, state?.eventId]
    );

    // only the base properties
    const initialValues = useMemo(
        (): FormValues => ({
            displayName: '',
            moduleId: null,
            consentType: null,
            title: { defaultValue: '', overrides: [] },
            orderNumber: null,
            isActive: true,
            description: { defaultValue: '', overrides: [] },
            purpose: undefined,
            dataField: null,
            conditionSettings: [],
        }),
        []
    );

    return (
        <ConsolePageWithHeader
            footer={[
                <LoadingButton key="submit" form="consentsAndDeclarationsForm" htmlType="submit" type="primary">
                    {t('consentsAndDeclarations:actions.create')}
                </LoadingButton>,
            ]}
            onBack={() => navigate(-1)}
            title={t('consentsAndDeclarations:addTitle')}
        >
            <Formik initialValues={initialValues} onSubmit={onSubmit} validate={validate}>
                {({ handleSubmit }) => (
                    <Form
                        id="consentsAndDeclarationsForm"
                        name="consentsAndDeclarationsForm"
                        onSubmitCapture={handleSubmit}
                    >
                        <ConsentsAndDeclarationsDetails initialValues={initialValues} />
                    </Form>
                )}
            </Formik>
        </ConsolePageWithHeader>
    );
};

export default AddConsentsAndDeclarations;
