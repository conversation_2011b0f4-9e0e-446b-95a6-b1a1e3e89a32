import { DealershipSettingSpecDataFragment } from '../../../../../../api/fragments/DealershipSettingSpecData';
import { DealershipPaymentSettingInput } from '../../../../../../api/types';

const DEFAULT_DEALERSHIP_PAYMENT_SETTING = { defaultId: undefined, overrides: [] };

export const getDefaultDealershipPaymentSettingValue = (
    value?: DealershipSettingSpecDataFragment
): DealershipPaymentSettingInput => {
    if (!value || value.__typename !== 'DealershipPaymentSetting') {
        return DEFAULT_DEALERSHIP_PAYMENT_SETTING;
    }

    return value;
};

export const processDealershipPaymentSettingInput = (
    input?: DealershipPaymentSettingInput
): DealershipPaymentSettingInput => {
    if (!input) {
        return null;
    }

    const { overrides = [], defaultId } = input;

    return {
        defaultId,
        overrides: overrides.map(({ relatedId, dealerId }) => ({
            dealerId,
            relatedId,
            isActive: Boolean(relatedId),
        })),
    };
};

export const getDealershipPaymentSetting = (dealerId: string, value?: DealershipSettingSpecDataFragment) => {
    if (value?.__typename !== 'DealershipPaymentSetting') {
        return null;
    }

    const { overrides = [], defaultPaymentSetting } = value;

    const override = overrides.find(({ dealerId: id }) => id === dealerId);

    if (!override) {
        return defaultPaymentSetting;
    }

    return override.isActive && override.relatedId ? override.paymentSetting : null;
};
