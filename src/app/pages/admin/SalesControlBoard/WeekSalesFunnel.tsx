import { PHeading } from '@porsche-design-system/components-react';
import { Space } from 'antd';
import dayjs from 'dayjs';
import { useTranslation } from 'react-i18next';
import { useSalesControlBoardContext } from './SalesControlBoardManager';
import { Card } from './ui';
import usePermission from './usePermission';

const WeekSalesFunnel = () => {
    const { t } = useTranslation('salesControlBoard');
    const { hasSalesManagerPermission } = usePermission();

    const { data, monthOfImportOptions } = useSalesControlBoardContext();

    if (!hasSalesManagerPermission || monthOfImportOptions.length === 0) {
        return null;
    }

    return (
        <Space direction="vertical" size={36} style={{ width: '100%' }}>
            {(data?.weekFunnels || []).map((week, index) => (
                // eslint-disable-next-line react/no-array-index-key
                <Card key={index.toString()}>
                    <PHeading size="large" tag="h2">
                        {t('salesControlBoard:weekSalesFunnel.title', {
                            count: index + 1,
                            dateRange: dayjs(week.start).isSame(dayjs(week.end), 'day')
                                ? dayjs(week.start).format('D MMMM YYYY')
                                : `${dayjs(week.start).format('D')} - ${dayjs(week.end).format('D MMMM YYYY')}`,
                        })}
                    </PHeading>
                </Card>
            ))}
        </Space>
    );
};

export default WeekSalesFunnel;
